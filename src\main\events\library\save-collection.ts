import { collectionsSublevel, levelKeys, db } from "@main/level";
import type { GameCollection } from "@types";

import { registerEvent } from "../register-event";

const saveCollection = async (
  _event: Electron.IpcMainInvokeEvent,
  collection: GameCollection
): Promise<void> => {
  try {
    const collectionsDb = collectionsSublevel(db);
    const collectionKey = levelKeys.collection(collection.id);

    // Ensure dates are properly formatted
    const collectionToSave: GameCollection = {
      ...collection,
      createdAt: new Date(collection.createdAt),
      updatedAt: new Date(collection.updatedAt),
    };

    await collectionsDb.put(collectionKey, collectionToSave);
  } catch (error) {
    console.error("Failed to save collection:", error);
    throw new Error("Failed to save collection");
  }
};

registerEvent("saveCollection", saveCollection);
