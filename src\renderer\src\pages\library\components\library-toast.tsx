import { useEffect } from "react";
import { CheckIcon, XIcon, AlertIcon } from "@primer/octicons-react";
import "./library-toast.scss";

interface LibraryToastProps {
  type: "success" | "error" | "warning";
  message: string;
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
}

export function LibraryToast({
  type,
  message,
  isVisible,
  onClose,
  duration = 4000,
}: LibraryToastProps) {
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckIcon size={16} />;
      case "error":
        return <XIcon size={16} />;
      case "warning":
        return <AlertIcon size={16} />;
      default:
        return <CheckIcon size={16} />;
    }
  };

  return (
    <div className={`library-toast library-toast--${type}`}>
      <div className="library-toast__content">
        <div className="library-toast__icon">{getIcon()}</div>
        <span className="library-toast__message">{message}</span>
        <button
          className="library-toast__close"
          onClick={onClose}
          type="button"
        >
          <XIcon size={14} />
        </button>
      </div>
    </div>
  );
}
