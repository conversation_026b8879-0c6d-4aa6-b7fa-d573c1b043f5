@use "../../../scss/globals.scss";

.library-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: calc(globals.$spacing-unit * 4);

  &__content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__spinner {
    position: relative;
    width: 60px;
    height: 60px;
  }

  &__spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid globals.$muted-color;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;

    &:nth-child(1) {
      animation-delay: 0s;
      opacity: 1;
    }

    &:nth-child(2) {
      animation-delay: -0.4s;
      opacity: 0.7;
      transform: scale(0.8);
    }

    &:nth-child(3) {
      animation-delay: -0.8s;
      opacity: 0.4;
      transform: scale(0.6);
    }
  }

  &__title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: globals.$muted-color;
  }

  &__subtitle {
    margin: 0;
    font-size: globals.$body-font-size;
    color: globals.$body-color;
    opacity: 0.8;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Fade in animation
.library-loading {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
