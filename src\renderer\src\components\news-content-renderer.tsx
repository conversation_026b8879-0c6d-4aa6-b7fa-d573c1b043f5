import React from 'react';

interface NewsContentRendererProps {
  content: string;
}

const NewsContentRenderer: React.FC<NewsContentRendererProps> = ({ content }) => {

  const processTextContent = (text: string): string => {
    let processed = text;

    // Replace YouTube videos with our custom iframe in their original position
    // 1. Process broken YouTube iframes with <br> tags and fix them (most important for your case)
    processed = processed
      .replace(/<div[^>]*class=["']youtube-embed-container["'][^>]*>[\s\S]*?<iframe[\s\S]*?src=["']https?:\/\/(?:www\.)?youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^"']*["'][\s\S]*?<\/iframe[\s\S]*?<\/div>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // 2. Process Steam's sharedFilePreviewYouTubeVideo divs with data-youtube
    processed = processed
      .replace(/<div[^>]*data-youtube=["']([a-zA-Z0-9_-]+)["'][^>]*>.*?<\/div>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // 3. Process standalone YouTube iframes and replace them with our custom iframe
    processed = processed
      .replace(/<iframe[^>]*src=["']https?:\/\/(?:www\.)?youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^"']*["'][^>]*><\/iframe>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // 4. Process BB code previewyoutube tags and replace them with our custom iframe
    processed = processed
      .replace(/\[previewyoutube=([a-zA-Z0-9_-]+);[^\]]*\]/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // Remove YouTube placeholder images (but keep videos in place)
    processed = processed
      .replace(/<img[^>]*src=["'][^"']*youtube_16x9_placeholder\.gif["'][^>]*\/?>/gi, '')
      .replace(/<img[^>]*class=["'][^"']*sharedFilePreviewYouTubeVideo[^"']*["'][^>]*\/?>/gi, '')
      .replace(/<img[^>]*youtube_16x9_placeholder[^>]*>/gi, '');

    // Process BB codes
    processed = processed
      .replace(/\[b\](.*?)\[\/b\]/gi, '<strong>$1</strong>')
      .replace(/\[i\](.*?)\[\/i\]/gi, '<em>$1</em>')
      .replace(/\[u\](.*?)\[\/u\]/gi, '<u>$1</u>')
      .replace(/\[strike\](.*?)\[\/strike\]/gi, '<del>$1</del>')
      .replace(/\[url=([^\]]+)\](.*?)\[\/url\]/gi, '<a href="$1" target="_blank" rel="noopener noreferrer">$2</a>')
      .replace(/\[url\](.*?)\[\/url\]/gi, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>')
      .replace(/\[img\](.*?)\[\/img\]/gi, '<img src="$1" alt="News image" style="max-width: 100%; height: auto;" />')
      .replace(/\n/g, '<br />');

    // Clean up remaining BB codes
    processed = processed.replace(/\[\/?\w+[^\]]*\]/gi, '');

    return processed;
  };

  const processedContent = processTextContent(content);

  return (
    <div className="news-content">
      <div dangerouslySetInnerHTML={{ __html: processedContent }} />
    </div>
  );
};

export default NewsContentRenderer;
