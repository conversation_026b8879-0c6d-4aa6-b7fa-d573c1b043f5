name: Build

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on: pull_request

jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest]
      fail-fast: false

    runs-on: ${{ matrix.os }}

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.9

      - name: Install dependencies
        run: pip install -r requirements.txt

      - name: Build with cx_Freeze
        run: python python_rpc/setup.py build

      - name: Build Linux
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libarchive-tools
          yarn build:linux
        env:
          MAIN_VITE_API_URL: ${{ vars.MAIN_VITE_STAGING_API_URL }}
          MAIN_VITE_AUTH_URL: ${{ vars.MAIN_VITE_STAGING_AUTH_URL }}
          MAIN_VITE_CHECKOUT_URL: ${{ vars.MAIN_VITE_STAGING_CHECKOUT_URL }}
          MAIN_VITE_WS_URL: ${{ vars.MAIN_VITE_WS_STAGING_URL }}
          RENDERER_VITE_EXTERNAL_RESOURCES_URL: ${{ vars.EXTERNAL_RESOURCES_URL }}
          MAIN_VITE_EXTERNAL_RESOURCES_URL: ${{ vars.EXTERNAL_RESOURCES_URL }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          RENDERER_VITE_SENTRY_DSN: ${{ vars.SENTRY_DSN }}
          RENDERER_VITE_REAL_DEBRID_REFERRAL_ID: ${{ vars.RENDERER_VITE_REAL_DEBRID_REFERRAL_ID }}
          RENDERER_VITE_TORBOX_REFERRAL_CODE: ${{ vars.RENDERER_VITE_TORBOX_REFERRAL_CODE }}

      - name: Build Windows
        if: matrix.os == 'windows-latest'
        run: yarn build:win
        env:
          MAIN_VITE_API_URL: ${{ vars.MAIN_VITE_STAGING_API_URL }}
          MAIN_VITE_AUTH_URL: ${{ vars.MAIN_VITE_STAGING_AUTH_URL }}
          MAIN_VITE_CHECKOUT_URL: ${{ vars.MAIN_VITE_STAGING_CHECKOUT_URL }}
          MAIN_VITE_WS_URL: ${{ vars.MAIN_VITE_WS_STAGING_URL }}
          RENDERER_VITE_EXTERNAL_RESOURCES_URL: ${{ vars.EXTERNAL_RESOURCES_URL }}
          MAIN_VITE_EXTERNAL_RESOURCES_URL: ${{ vars.EXTERNAL_RESOURCES_URL }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          RENDERER_VITE_SENTRY_DSN: ${{ vars.SENTRY_DSN }}
          RENDERER_VITE_REAL_DEBRID_REFERRAL_ID: ${{ vars.RENDERER_VITE_REAL_DEBRID_REFERRAL_ID }}
          RENDERER_VITE_TORBOX_REFERRAL_CODE: ${{ vars.RENDERER_VITE_TORBOX_REFERRAL_CODE }}

      - name: Upload build
        env:
          BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
          S3_ENDPOINT: ${{ secrets.S3_ENDPOINT }}
          S3_ACCESS_KEY_ID: ${{ secrets.S3_ACCESS_KEY_ID }}
          S3_SECRET_ACCESS_KEY: ${{ secrets.S3_SECRET_ACCESS_KEY }}
          S3_BUILDS_BUCKET_NAME: ${{ secrets.S3_BUILDS_BUCKET_NAME }}
          BUILDS_URL: ${{ secrets.BUILDS_URL }}
          BUILD_WEBHOOK_URL: ${{ secrets.BUILD_WEBHOOK_URL }}
          GITHUB_ACTOR: ${{ github.actor }}
        run: node scripts/upload-build.cjs

      - name: Create artifact
        uses: actions/upload-artifact@v4
        with:
          name: Build-${{ matrix.os }}
          path: |
            dist/*-portable.exe
            dist/*.zip
            dist/*.dmg
            dist/*.deb
            dist/*.rpm
            dist/*.tar.gz
            dist/*.yml
            dist/*.blockmap
            dist/*.pacman
            dist/*.AppImage
