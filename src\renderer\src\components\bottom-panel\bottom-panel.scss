@use "../../scss/globals.scss";

.bottom-panel {
  width: 100%;
  height: 28px;
  min-height: 28px;
  border-top: solid 1px globals.$border-color;
  background-color: globals.$background-color;
  padding: calc(globals.$spacing-unit / 2) calc(globals.$spacing-unit * 2);
  display: flex;
  align-items: center;
  transition: all ease 0.2s;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: globals.$bottom-panel-z-index;

  &__downloads-button {
    color: globals.$body-color;
    border-bottom: solid 1px transparent;

    &:hover {
      border-bottom: solid 1px globals.$body-color;
      cursor: pointer;
    }
  }

  &__version-button {
    color: globals.$body-color;
    border-bottom: solid 1px transparent;

    &:hover {
      border-bottom: solid 1px globals.$body-color;
      cursor: pointer;
    }
  }
}
