@use "../../scss/globals.scss";

$hero-height: 300px;

@keyframes slide-in {
  0% {
    transform: translateY(calc(40px + globals.$spacing-unit * 2));
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.game-details {
  &__wrapper {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    height: 100%;
    transition: all ease 0.3s;

    &--blurred {
      filter: blur(20px);
    }
  }

  &__hero {
    width: 100%;
    height: $hero-height;
    min-height: $hero-height;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all ease 0.2s;

    @media (min-width: 1250px) {
      height: 350px;
      min-height: 350px;
    }
  }

  &__hero-content {
    padding: calc(globals.$spacing-unit * 2);
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }

  &__hero-logo-backdrop {
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.3) 60%, transparent 100%);
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__hero-image {
    width: 100%;
    height: $hero-height;
    min-height: $hero-height;
    object-fit: cover;
    object-position: top;
    transition: all ease 0.2s;
    position: absolute;
    z-index: 0;

    @media (min-width: 1250px) {
      object-position: center;
      height: 350px;
      min-height: 350px;
    }
  }

  &__game-logo {
    width: 300px;
    align-self: flex-end;
  }

  &__hero-image-skeleton {
    height: 300px;

    @media (min-width: 1250px) {
      height: 350px;
    }
  }

  &__container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    z-index: 1;
  }

  &__description-container {
    display: flex;
    width: 100%;
    flex: 1;
    background: linear-gradient(
      0deg,
      globals.$background-color 50%,
      globals.$dark-background-color 100%
    );
  }

  &__description-content {
    width: 100%;
    height: 100%;
  }

  &__description {
    user-select: text;
    line-height: 22px;
    font-size: globals.$body-font-size;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 2);
    width: 100%;
    margin-left: auto;
    margin-right: auto;

    @media (min-width: 1280px) {
      width: 60%;
    }

    img {
      border-radius: 5px;
      margin-top: globals.$spacing-unit;
      margin-bottom: calc(globals.$spacing-unit * 3);
      display: block;
      width: 100%;
      height: auto;
      object-fit: cover;
    }

    a {
      color: globals.$body-color;
    }

    .bb_tag {
      margin-top: calc(globals.$spacing-unit * 2);
      margin-bottom: calc(globals.$spacing-unit * 2);
    }
  }

  &__description-skeleton {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 2);
    width: 100%;
    margin-left: auto;
    margin-right: auto;

    @media (min-width: 1280px) {
      width: 60%;
      line-height: 22px;
    }
  }

  &__randomizer-button {
    animation: slide-in 0.2s;
    position: fixed;
    bottom: calc(globals.$spacing-unit * 3);
    right: calc(9px + globals.$spacing-unit * 2);
    box-shadow: rgba(255, 255, 255, 0.1) 0px 0px 10px 1px;
    border: solid 2px globals.$border-color;
    z-index: 1;
    background-color: globals.$background-color;

    &:hover {
      background-color: globals.$background-color;
      box-shadow: rgba(255, 255, 255, 0.1) 0px 0px 15px 5px;
      opacity: 1;
    }

    &:active {
      transform: scale(0.98);
    }

    &:disabled {
      box-shadow: none;
      transform: none;
      opacity: 0.8;
      background-color: globals.$background-color;
    }
  }

  &__hero-panel-skeleton {
    width: 100%;
    padding: calc(globals.$spacing-unit * 2);
    display: flex;
    align-items: center;
    background-color: globals.$background-color;
    height: 72px;
    border-bottom: solid 1px globals.$border-color;
  }

  &__cloud-sync-button {
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(20px);
    border-radius: 8px;
    transition: all ease 0.2s;
    cursor: pointer;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: globals.$spacing-unit;
    color: globals.$muted-color;
    font-size: globals.$small-font-size;
    border: solid 1px globals.$border-color;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.8);
    animation: slide-in 0.3s cubic-bezier(0.33, 1, 0.68, 1);

    &:active {
      opacity: 0.9;
    }

    &:disabled {
      opacity: globals.$disabled-opacity;
      cursor: not-allowed;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  &__stars-icon-container {
    width: 16px;
    height: 16px;
    position: relative;
  }

  &__stars-icon {
    width: 70px;
    position: absolute;
    top: -28px;
    left: -27px;
  }

  &__cloud-icon-container {
    width: 20px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  &__cloud-icon {
    width: 26px;
    position: absolute;
    top: -3px;
  }

  &__hero-backdrop {
    flex: 1;
    transition: opacity 0.2s ease;
  }
}
