import { useState, useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { createPortal } from "react-dom";
import { XIcon, CheckIcon, PlusIcon, FileDirectoryIcon } from "@primer/octicons-react";

import { But<PERSON> } from "@renderer/components";
import { useLibraryCollections } from "@renderer/hooks";
import { CollectionModal } from "./collection-modal";

import type { LibraryGame } from "@types";

import "./collection-selector-modal.scss";

interface CollectionSelectorModalProps {
  game: LibraryGame;
  onClose: () => void;
}

export function CollectionSelectorModal({ game, onClose }: CollectionSelectorModalProps) {
  const { t } = useTranslation("library");
  const {
    collections,
    addGameToCollectionById,
    removeGameFromCollectionById,
    getCollectionsForGame,
    isLoading
  } = useLibraryCollections();
  
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [localLoading, setLocalLoading] = useState(false);
  const [gameCollections, setGameCollections] = useState<string[]>([]);

  useEffect(() => {
    const gameInCollections = getCollectionsForGame(game.id);
    setGameCollections(gameInCollections.map(c => c.id));
  }, [game.id, getCollectionsForGame]);

  const handleToggleCollection = useCallback(async (collectionId: string) => {
    try {
      setLocalLoading(true);
      
      if (gameCollections.includes(collectionId)) {
        await removeGameFromCollectionById(collectionId, game.id);
        setGameCollections(prev => prev.filter(id => id !== collectionId));
      } else {
        await addGameToCollectionById(collectionId, game.id);
        setGameCollections(prev => [...prev, collectionId]);
      }
    } catch (error) {
      console.error("Failed to toggle collection:", error);
    } finally {
      setLocalLoading(false);
    }
  }, [gameCollections, addGameToCollectionById, removeGameFromCollectionById, game]);

  const handleCreateCollection = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const handleCreateModalClose = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  const handleCreateModalSave = useCallback(() => {
    setShowCreateModal(false);
    // Collections will be automatically updated via the hook
  }, []);

  if (showCreateModal) {
    return (
      <CollectionModal
        onClose={handleCreateModalClose}
        onSave={handleCreateModalSave}
      />
    );
  }

  return createPortal(
    <div className="collection-selector-modal-backdrop" onClick={onClose}>
      <div className="collection-selector-modal" onClick={(e) => e.stopPropagation()}>
        <div className="collection-selector-modal__header">
          <div className="collection-selector-modal__title-section">
            <h2 className="collection-selector-modal__title">
              {t("add_to_collection")}
            </h2>
            <p className="collection-selector-modal__subtitle">
              {game.title}
            </p>
          </div>
          <button
            type="button"
            className="collection-selector-modal__close"
            onClick={onClose}
          >
            <XIcon size={20} />
          </button>
        </div>

        <div className="collection-selector-modal__content">
          {collections.length === 0 ? (
            <div className="collection-selector-modal__empty">
              <FileDirectoryIcon size={48} />
              <h3>{t("no_collections")}</h3>
              <p>{t("create_first_collection")}</p>
              <Button onClick={handleCreateCollection} theme="primary">
                <PlusIcon size={16} />
                {t("create_collection")}
              </Button>
            </div>
          ) : (
            <>
              <div className="collection-selector-modal__list">
                {collections.map((collection) => {
                  const isSelected = gameCollections.includes(collection.id);
                  
                  return (
                    <button
                      key={collection.id}
                      type="button"
                      className={`collection-selector-modal__item ${
                        isSelected ? "collection-selector-modal__item--selected" : ""
                      }`}
                      onClick={() => handleToggleCollection(collection.id)}
                      disabled={localLoading}
                    >
                      <div 
                        className="collection-selector-modal__item-color"
                        style={{ backgroundColor: collection.color }}
                      />
                      <div className="collection-selector-modal__item-info">
                        <h4 className="collection-selector-modal__item-name">
                          {collection.name}
                        </h4>
                        {collection.description && (
                          <p className="collection-selector-modal__item-description">
                            {collection.description}
                          </p>
                        )}
                        <span className="collection-selector-modal__item-count">
                          {collection.gameIds.length} {t("games")}
                        </span>
                      </div>
                      <div className="collection-selector-modal__item-checkbox">
                        {isSelected && <CheckIcon size={16} />}
                      </div>
                    </button>
                  );
                })}
              </div>

              <div className="collection-selector-modal__actions">
                <Button 
                  onClick={handleCreateCollection} 
                  theme="outline"
                  disabled={localLoading}
                >
                  <PlusIcon size={16} />
                  {t("create_new_collection")}
                </Button>
              </div>
            </>
          )}
        </div>

        <div className="collection-selector-modal__footer">
          <Button onClick={onClose} theme="primary">
            {t("done")}
          </Button>
        </div>
      </div>
    </div>,
    document.body
  );
}
