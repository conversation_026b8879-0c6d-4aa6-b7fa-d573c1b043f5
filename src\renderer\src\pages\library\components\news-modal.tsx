import React, { useEffect, useRef, useState } from 'react';
import {
  XIcon,
  LinkExternalIcon,
  CalendarIcon,
  ClockIcon,
  PersonIcon,
  TagIcon,
  ShareIcon,
  BookmarkIcon
} from '@primer/octicons-react';
import { SteamNewsItem } from '@renderer/services/steam-news-service';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale/es';
import { enUS } from 'date-fns/locale/en-US';
import { useTranslation } from 'react-i18next';
import NewsContentRenderer from '@renderer/components/news-content-renderer';

import './news-modal.scss';

interface NewsModalProps {
  newsItem: SteamNewsItem;
  onClose: () => void;
}

const NewsModal: React.FC<NewsModalProps> = ({ newsItem, onClose }) => {
  const { t, i18n } = useTranslation("library");
  const modalRef = useRef<HTMLDivElement>(null);
  const locale = i18n.language === 'es' ? es : enUS;
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    document.addEventListener('mousedown', handleClickOutside);
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [onClose]);

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString(i18n.language, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatRelativeDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return formatDistanceToNow(date, {
      addSuffix: true,
      locale
    });
  };

  const getDisplayAuthor = () => {
    // If author is generic or empty, use more meaningful information
    if (!newsItem.author ||
        newsItem.author === 'Steam Community' ||
        newsItem.author.trim() === '' ||
        newsItem.author === 'steam_community_rss') {
      return newsItem.gameTitle || newsItem.feedname || 'Steam';
    }
    return newsItem.author;
  };

  const getProcessedContent = () => {
    // Use pre-processed content if available, otherwise process it here
    if (newsItem.processedContent) {
      return newsItem.processedContent;
    }

    // Fallback processing for older news items
    let content = newsItem.contents || '';

    // Convert Steam BB codes to HTML
    content = content
      // Bold text
      .replace(/\[b\](.*?)\[\/b\]/gi, '<strong>$1</strong>')
      // Italic text
      .replace(/\[i\](.*?)\[\/i\]/gi, '<em>$1</em>')
      // Underline text
      .replace(/\[u\](.*?)\[\/u\]/gi, '<u>$1</u>')
      // Strike through
      .replace(/\[strike\](.*?)\[\/strike\]/gi, '<del>$1</del>')
      // Links with custom text
      .replace(/\[url=([^\]]+)\](.*?)\[\/url\]/gi, '<a href="$1" target="_blank" rel="noopener noreferrer">$2</a>')
      // Simple links
      .replace(/\[url\](.*?)\[\/url\]/gi, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>')
      // Images
      .replace(/\[img\](.*?)\[\/img\]/gi, '<img src="$1" alt="News image" style="max-width: 100%; height: auto;" />')

      // YouTube processing - replace original videos with our custom iframe in their exact position

      // 1. Process broken YouTube iframes with <br> tags and fix them (most important)
      .replace(/<div[^>]*class=["']youtube-embed-container["'][^>]*>[\s\S]*?<iframe[\s\S]*?src=["']https?:\/\/(?:www\.)?youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^"']*["'][\s\S]*?<\/iframe[\s\S]*?<\/div>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      })

      // 2. Process Steam's sharedFilePreviewYouTubeVideo divs with data-youtube
      .replace(/<div[^>]*data-youtube=["']([a-zA-Z0-9_-]+)["'][^>]*>.*?<\/div>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      })

      // 3. Process standalone YouTube iframes and replace them with our custom iframe
      .replace(/<iframe[^>]*src=["']https?:\/\/(?:www\.)?youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^"']*["'][^>]*><\/iframe>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      })

      // 3. Remove YouTube placeholder images - more comprehensive
      .replace(/<img[^>]*src=["'][^"']*youtube_16x9_placeholder\.gif["'][^>]*\/?>/gi, '')
      .replace(/<img[^>]*class=["'][^"']*sharedFilePreviewYouTubeVideo[^"']*["'][^>]*\/?>/gi, '')
      .replace(/<img[^>]*youtube_16x9_placeholder[^>]*>/gi, '')

      // 4. Process BB code previewyoutube tags and replace them with our custom iframe
      .replace(/\[previewyoutube=([a-zA-Z0-9_-]+);[^\]]*\]/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      })

      // Line breaks
      .replace(/\n/g, '<br />');

    // Clean up any remaining BB codes
    content = content.replace(/\[\/?\w+[^\]]*\]/gi, '');

    return content;
  };

  const getContentForRenderer = () => {
    // Return the raw content for the NewsContentRenderer to process
    return newsItem.processedContent || newsItem.contents || '';
  };

  const openExternalLink = () => {
    if (newsItem.url) {
      window.electron.openExternal(newsItem.url);
    }
  };

  const getFeaturedImage = () => {
    // Featured image always uses game library hero (high quality banner)
    return `https://cdn.akamai.steamstatic.com/steam/apps/${newsItem.appid}/library_hero.jpg`;
  };

  const getReadingTime = () => {
    const wordsPerMinute = 200;
    const words = (newsItem.contents || '').split(/\s+/).length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return minutes;
  };

  const getNewsType = () => {
    if (newsItem.isEvent) return t('event');
    if (newsItem.feedlabel?.toLowerCase().includes('update')) return t('update');
    if (newsItem.feedlabel?.toLowerCase().includes('announcement')) return t('announcement');
    return t('news');
  };

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    // TODO: Implement bookmark functionality
  };

  const handleShare = () => {
    setShowShareMenu(!showShareMenu);
  };

  const copyToClipboard = () => {
    if (newsItem.url) {
      navigator.clipboard.writeText(newsItem.url);
      setShowShareMenu(false);
      // TODO: Show toast notification
    }
  };

  return (
    <div className="news-modal-overlay">
      <div className="news-modal" ref={modalRef}>
        {/* Close button - floating */}
        <button
          className="news-modal-close-floating"
          onClick={onClose}
          title={t('close')}
        >
          <XIcon size={24} />
        </button>

        {/* Featured image with overlay */}
        <div className="news-modal-hero">
          <img
            src={getFeaturedImage()}
            alt={newsItem.title}
            className="news-modal-hero-image"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              const fallbackImages = [
                `https://cdn.akamai.steamstatic.com/steam/apps/${newsItem.appid}/library_hero.jpg`,
                `https://cdn.akamai.steamstatic.com/steam/apps/${newsItem.appid}/header.jpg`,
                `https://cdn.cloudflare.steamstatic.com/steam/apps/${newsItem.appid}/library_600x900.jpg`
              ];

              const currentSrc = target.src;
              const currentIndex = fallbackImages.findIndex(img => currentSrc.includes(img.split('/').pop() || ''));
              const nextIndex = currentIndex + 1;

              if (nextIndex < fallbackImages.length) {
                target.src = fallbackImages[nextIndex];
              }
            }}
          />
          <div className="news-modal-hero-overlay">
            <div className="news-modal-hero-content">
              <div className="news-modal-breadcrumb">
                <span className="news-modal-game-name">{newsItem.gameTitle || newsItem.feedname}</span>
                <span className="news-modal-breadcrumb-separator">›</span>
                <span className="news-modal-news-type">{getNewsType()}</span>
              </div>
              <h1 className="news-modal-title">{newsItem.title}</h1>
              <div className="news-modal-hero-meta">
                <div className="news-modal-meta-item">
                  <PersonIcon size={16} />
                  <span>{getDisplayAuthor()}</span>
                </div>
                <div className="news-modal-meta-item">
                  <ClockIcon size={16} />
                  <span>{formatRelativeDate(newsItem.date)}</span>
                </div>
                <div className="news-modal-meta-item">
                  <TagIcon size={16} />
                  <span>{getReadingTime()} min read</span>
                </div>
                {newsItem.isEvent && newsItem.eventDate && (
                  <div className="news-modal-meta-item news-modal-event-meta">
                    <CalendarIcon size={16} />
                    <span>{formatDate(newsItem.eventDate)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Content area */}
        <div className="news-modal-content-wrapper">
          {/* Article metadata */}
          <div className="news-modal-article-meta">
            <div className="news-modal-meta-left">
              <div className="news-modal-meta-item">
                <PersonIcon size={16} />
                <span>{getDisplayAuthor()}</span>
              </div>
              <div className="news-modal-meta-item">
                <ClockIcon size={16} />
                <span>{formatRelativeDate(newsItem.date)}</span>
              </div>
              <div className="news-modal-meta-item">
                <TagIcon size={16} />
                <span>{getReadingTime()} min read</span>
              </div>
              {newsItem.isEvent && newsItem.eventDate && (
                <div className="news-modal-meta-item news-modal-event-meta">
                  <CalendarIcon size={16} />
                  <span>{formatDate(newsItem.eventDate)}</span>
                </div>
              )}
            </div>

            <div className="news-modal-meta-actions">
              <button
                className={`news-modal-action-button ${isBookmarked ? 'active' : ''}`}
                onClick={handleBookmark}
                title={isBookmarked ? t('removeBookmark') : t('addBookmark')}
              >
                <BookmarkIcon size={18} />
              </button>

              <div className="news-modal-share-container">
                <button
                  className="news-modal-action-button"
                  onClick={handleShare}
                  title={t('share')}
                >
                  <ShareIcon size={18} />
                </button>
                {showShareMenu && (
                  <div className="news-modal-share-menu">
                    <button onClick={copyToClipboard}>{t('copyLink')}</button>
                    {newsItem.url && (
                      <button onClick={openExternalLink}>{t('openInSteam')}</button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Article content */}
          <article className="news-modal-article">
            <div className="news-modal-content">
              <NewsContentRenderer content={getContentForRenderer()} />
            </div>
          </article>

          {/* Footer with additional actions */}
          <footer className="news-modal-footer">
            <div className="news-modal-footer-content">
              <div className="news-modal-tags">
                {newsItem.feedlabel && (
                  <span className="news-modal-tag">{newsItem.feedlabel}</span>
                )}
                {newsItem.isEvent && (
                  <span className="news-modal-tag news-modal-tag-event">{t('event')}</span>
                )}
              </div>

              <div className="news-modal-footer-actions">
                {newsItem.url && (
                  <button
                    className="news-modal-primary-button"
                    onClick={openExternalLink}
                  >
                    <LinkExternalIcon size={16} />
                    <span style={{ marginLeft: '8px' }}>{t('readFullArticle')}</span>
                  </button>
                )}
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
  );
};

export default NewsModal;
