@use "../../scss/globals.scss";

.sidebar-quick-access {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  padding: calc(globals.$spacing-unit * 2);
  backdrop-filter: blur(24px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  margin-bottom: calc(globals.$spacing-unit * 2);

  // Animation for smooth appearance
  animation: quickAccessFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &__header {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    margin-bottom: calc(globals.$spacing-unit * 2);
    padding-bottom: calc(globals.$spacing-unit);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  &__title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__section {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit);
  }

  &__section-title {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit / 2);
    margin: 0;
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  &__games {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 2);
  }

  &__game {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    padding: calc(globals.$spacing-unit) calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.06);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 48px; // Touch-friendly minimum height

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &--active {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.75);
    }
  }

  &__game-icon {
    position: relative;
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    border-radius: 8px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 36px;
      height: 36px;
      min-width: 36px;
      min-height: 36px;
    }
  }

  &__game-icon-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.4);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  }

  &__game-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    border: 2px solid rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &__game-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.1);
    overflow: hidden;
  }

  &__game-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3e62c0 0%, #5b7fd8 100%);
    transition: width 0.3s ease;
  }

  &__game-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  &__game-title {
    font-size: 13px;
    font-weight: 500;
    color: globals.$body-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: left;
  }

  &__game-subtitle {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: left;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding: calc(globals.$spacing-unit * 1.5);
    border-radius: 12px;
    
    &__game {
      min-height: 44px;
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit);
    }

    &__game-icon {
      width: 28px;
      height: 28px;
      min-width: 28px;
      min-height: 28px;
    }

    &__game-title {
      font-size: 12px;
    }

    &__game-subtitle {
      font-size: 10px;
    }
  }
}

@keyframes quickAccessFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
