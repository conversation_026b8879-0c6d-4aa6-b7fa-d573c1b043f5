{"name": "hydralauncher", "version": "3.6.0", "description": "Hydra", "main": "./out/main/index.js", "author": "Los Broxas", "repository": {"type": "git", "url": "https://github.com/hydralauncher/hydra.git"}, "type": "module", "engines": {"npm": "please-use-yarn", "yarn": ">= 1.19.1"}, "scripts": {"format": "prettier --write .", "format-check": "prettier --check .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps && node ./scripts/postinstall.cjs", "build:unpack": "npm run build && electron-builder --dir", "build:win": "electron-vite build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux", "prepare": "husky", "protoc": "npx protoc --ts_out src/main/generated --proto_path proto proto/*.proto"}, "dependencies": {"@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@fontsource/noto-sans": "^5.1.0", "@hookform/resolvers": "^3.9.1", "@monaco-editor/react": "^4.6.0", "@primer/octicons-react": "^19.9.0", "@radix-ui/react-dropdown-menu": "^2.1.2", "@reduxjs/toolkit": "^2.2.3", "@sentry/react": "^8.47.0", "@sentry/vite-plugin": "^2.22.7", "auto-launch": "^5.0.6", "axios": "^1.7.9", "axios-cookiejar-support": "^5.0.5", "classic-level": "^2.0.0", "classnames": "^2.5.1", "color": "^4.2.3", "color.js": "^1.2.0", "crc": "^4.3.2", "create-desktop-shortcuts": "^1.11.1", "date-fns": "^3.6.0", "dexie": "^4.0.10", "diskusage": "^1.2.0", "electron-log": "^5.2.4", "electron-updater": "^6.6.2", "file-type": "^20.5.0", "framer-motion": "^12.15.0", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "jsdom": "^24.0.0", "jsonwebtoken": "^9.0.2", "lodash-es": "^4.17.21", "parse-torrent": "^11.0.18", "rc-virtual-list": "^3.18.3", "react-hook-form": "^7.53.0", "react-i18next": "^14.1.0", "react-loading-skeleton": "^3.4.0", "react-redux": "^9.1.1", "react-router-dom": "^6.22.3", "react-shadow": "^20.6.0", "react-tooltip": "^5.28.1", "sound-play": "^1.1.0", "steam-shortcut-editor": "https://github.com/hydralauncher/steam-shortcut-editor", "sudo-prompt": "^9.2.1", "tar": "^7.4.3", "tough-cookie": "^5.1.1", "user-agents": "^1.1.387", "winreg": "^1.2.5", "ws": "^8.18.1", "yaml": "^2.6.1", "yup": "^1.5.0", "zod": "^3.24.1"}, "devDependencies": {"@aws-sdk/client-s3": "^3.705.0", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@electron-toolkit/eslint-config-prettier": "^2.0.0", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@protobuf-ts/plugin": "^2.10.0", "@swc/core": "^1.4.16", "@types/auto-launch": "^5.0.5", "@types/color": "^3.0.6", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.8", "@types/lodash-es": "^4.17.12", "@types/node": "^20.12.7", "@types/parse-torrent": "^5.8.7", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/sound-play": "^1.1.3", "@types/user-agents": "^1.0.4", "@types/winreg": "^1.2.36", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.2.1", "electron": "^32.3.3", "electron-builder": "^26.0.12", "electron-vite": "^3.0.0", "eslint": "^8.56.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "prettier": "^3.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "sass-embedded": "^1.80.6", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vite": "^5.0.12", "vite-plugin-svgr": "^4.2.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}