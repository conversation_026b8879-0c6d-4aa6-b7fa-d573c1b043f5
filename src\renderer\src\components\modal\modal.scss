@use "../../scss/globals.scss";

/**
 * Modal Styles
 *
 * Touch-optimized modal component for all dialogs and overlays.
 * Designed for seamless experience across desktop, tablet, and handheld devices.
 *
 * Key Features:
 * - Touch-friendly modal controls (48px+ minimum)
 * - Steam Deck optimizations (52px+ targets)
 * - Responsive modal sizing and positioning
 * - Enhanced readability for handheld devices
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 */

.modal {
  animation: scale-fade-in 0.3s cubic-bezier(0.4, 0, 0.2, 1) 0s 1 normal none running;
  background: linear-gradient(135deg, rgba(28, 28, 28, 0.95) 0%, rgba(21, 21, 21, 0.95) 100%);
  border-radius: 20px;
  min-width: 480px;
  max-width: 700px;
  color: globals.$body-color;
  max-height: 90vh;
  border: 1px solid rgba(255, 255, 255, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(24px);
  box-shadow:
    0 24px 96px rgba(0, 0, 0, 0.6),
    0 12px 48px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  &--closing {
    animation-name: scale-fade-out;
    opacity: 0;
  }

  &--large {
    width: 900px;
    max-width: 900px;
  }

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    min-width: 520px;
    max-width: 750px;
    border-radius: 24px;

    &--large {
      width: 950px;
      max-width: 950px;
    }
  }

  @media (max-width: 768px) {
    min-width: 90%;
    max-width: 95%;
    border-radius: 18px;
    max-height: 95vh;

    &--large {
      width: 95%;
      max-width: 95%;
    }
  }

  &__content {
    height: 100%;
    overflow: auto;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 2.5);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      padding: calc(globals.$spacing-unit * 3.5) calc(globals.$spacing-unit * 3);
    }

    @media (max-width: 768px) {
      padding: calc(globals.$spacing-unit * 2.5) calc(globals.$spacing-unit * 2);
    }
  }

  // Modal Header - Touch Optimized
  &__header {
    display: flex;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2.5) calc(globals.$spacing-unit * 2.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.02);

    &-title {
      display: flex;
      gap: calc(globals.$spacing-unit * 0.75);
      flex-direction: column;
      flex: 1;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 700;
        color: globals.$muted-color;
        background: linear-gradient(135deg, globals.$muted-color 0%, rgba(255, 255, 255, 0.8) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        // Steam Deck optimizations
        @media (max-width: 1280px) and (max-height: 800px) {
          font-size: 22px;
        }

        @media (max-width: 768px) {
          font-size: 18px;
        }
      }

      p {
        margin: 0;
        font-size: 15px;
        color: globals.$body-color;
        line-height: 1.4;

        // Steam Deck optimizations
        @media (max-width: 1280px) and (max-height: 800px) {
          font-size: 16px;
        }

        @media (max-width: 768px) {
          font-size: 14px;
        }
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 3);
    }

    @media (max-width: 768px) {
      padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 2);
    }
  }

  // Close Button - Touch Optimized
  &__close-button {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: globals.$body-color;
    cursor: pointer;
    padding: calc(globals.$spacing-unit * 1.25);
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px; // Touch target minimum
    min-height: 48px;
    backdrop-filter: blur(16px);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: globals.$muted-color;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-width: 52px;
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.5);
      border-radius: 14px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.9);
        transition: transform 0.1s ease;
      }
    }
  }

  &__close-button-icon {
    width: 24px;
    height: 24px;
    color: globals.$body-color;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 26px;
      height: 26px;
    }
  }
}

@keyframes scale-fade-in {
  0% {
    opacity: 0;
    scale: 0.5;
  }
  100% {
    opacity: 1;
    scale: 1;
  }
}

@keyframes scale-fade-out {
  0% {
    opacity: 1;
    scale: 1;
  }
  100% {
    opacity: 0;
    scale: 0.5;
  }
}
