@use "../../../scss/globals.scss";

.library-stats {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: calc(globals.$spacing-unit * 3);
  margin: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 4);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  }

  @media (max-width: 768px) {
    margin: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 3);
    padding: calc(globals.$spacing-unit * 2);
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 3);
  }

  &__primary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: calc(globals.$spacing-unit * 2);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__secondary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: calc(globals.$spacing-unit * 1.5);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: calc(globals.$spacing-unit * 1);
    }
  }

  &__stat {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    padding: calc(globals.$spacing-unit * 2);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
      border-color: rgba(255, 255, 255, 0.15);

      &::before {
        opacity: 1;
      }
    }

    &--total {
      .library-stats__stat-icon {
        background: linear-gradient(135deg, rgba(62, 98, 192, 0.2) 0%, rgba(62, 98, 192, 0.1) 100%);
        color: rgba(62, 98, 192, 0.9);
        border-color: rgba(62, 98, 192, 0.3);
      }
    }

    &--installed {
      .library-stats__stat-icon {
        background: linear-gradient(135deg, rgba(28, 151, 73, 0.2) 0%, rgba(28, 151, 73, 0.1) 100%);
        color: rgba(28, 151, 73, 0.9);
        border-color: rgba(28, 151, 73, 0.3);
      }
    }

    &--playtime {
      .library-stats__stat-icon {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%);
        color: rgba(255, 193, 7, 0.9);
        border-color: rgba(255, 193, 7, 0.3);
      }
    }

    &--recent {
      .library-stats__stat-icon {
        background: linear-gradient(135deg, rgba(138, 43, 226, 0.2) 0%, rgba(138, 43, 226, 0.1) 100%);
        color: rgba(138, 43, 226, 0.9);
        border-color: rgba(138, 43, 226, 0.3);
      }
    }

    &--new {
      .library-stats__stat-icon {
        background: linear-gradient(135deg, rgba(23, 162, 184, 0.2) 0%, rgba(23, 162, 184, 0.1) 100%);
        color: rgba(23, 162, 184, 0.9);
        border-color: rgba(23, 162, 184, 0.3);
      }
    }

    &--champion {
      .library-stats__stat-icon {
        background: linear-gradient(135deg, rgba(255, 165, 0, 0.2) 0%, rgba(255, 165, 0, 0.1) 100%);
        color: rgba(255, 165, 0, 0.9);
        border-color: rgba(255, 165, 0, 0.3);
      }
    }

    @media (max-width: 768px) {
      padding: calc(globals.$spacing-unit * 1.5);
      gap: calc(globals.$spacing-unit * 1);
    }
  }

  &__stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border: 1px solid;
    border-radius: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;

    @media (max-width: 768px) {
      width: 40px;
      height: 40px;
    }

    .library-stats__stat:hover & {
      transform: scale(1.1);
    }
  }

  &__stat-content {
    flex: 1;
    min-width: 0;
  }

  &__stat-value {
    font-size: calc(globals.$body-font-size * 1.5);
    font-weight: 700;
    color: globals.$muted-color;
    line-height: 1.2;
    margin-bottom: calc(globals.$spacing-unit * 0.25);

    .library-stats__stat--champion & {
      font-size: globals.$body-font-size;
      font-weight: 600;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    @media (max-width: 768px) {
      font-size: calc(globals.$body-font-size * 1.25);
    }
  }

  &__stat-label {
    font-size: globals.$small-font-size;
    font-weight: 500;
    color: globals.$body-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
    line-height: 1.2;

    .library-stats__stat--champion & {
      text-transform: none;
      letter-spacing: normal;
      font-size: 11px;
    }
  }

  &__filter-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: 12px;
    margin-top: calc(globals.$spacing-unit * 1);
  }

  &__filter-text {
    font-size: globals.$small-font-size;
    font-weight: 600;
    color: rgba(255, 193, 7, 0.9);
    text-align: center;
  }

  // Compact Mode Styles
  &--compact {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    border: 1px solid rgba(255, 255, 255, 0.06);
    border-radius: 12px;
    padding: calc(globals.$spacing-unit * 2);
    margin: 0;
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);

    &::before {
      display: none;
    }
  }

  &__compact-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 3);
    flex-wrap: wrap;

    @media (max-width: 768px) {
      gap: calc(globals.$spacing-unit * 2);
      justify-content: space-between;
    }
  }

  &__compact-stat {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    font-size: globals.$small-font-size;
    font-weight: 600;
    color: globals.$body-color;
    white-space: nowrap;

    svg {
      opacity: 0.7;
      flex-shrink: 0;
    }

    span {
      color: globals.$muted-color;
    }

    @media (max-width: 768px) {
      font-size: 11px;
      gap: calc(globals.$spacing-unit * 0.75);
    }
  }

  &__compact-filter {
    display: flex;
    align-items: center;
    padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 1);
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    color: rgba(255, 193, 7, 0.9);
    white-space: nowrap;

    @media (max-width: 768px) {
      font-size: 10px;
      padding: calc(globals.$spacing-unit * 0.25) calc(globals.$spacing-unit * 0.75);
    }
  }
}

// Animation for stats appearing
.library-stats__stat {
  animation: statSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;

  @for $i from 1 through 6 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

@keyframes statSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive grid adjustments
@media (max-width: 1200px) {
  .library-stats {
    &__primary {
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    &__secondary {
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }
  }
}

@media (max-width: 480px) {
  .library-stats {
    margin: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 1.5);

    &__content {
      gap: calc(globals.$spacing-unit * 2);
    }

    &__stat {
      padding: calc(globals.$spacing-unit * 1);
    }

    &__stat-icon {
      width: 36px;
      height: 36px;
    }

    &__stat-value {
      font-size: globals.$body-font-size;
    }

    &__stat-label {
      font-size: 10px;
    }
  }
}
