import { collectionsSublevel, levelKeys, db } from "@main/level";
import type { GameCollection } from "@types";

import { registerEvent } from "../register-event";

const getCollections = async (): Promise<GameCollection[]> => {
  try {
    const collections: GameCollection[] = [];
    const collectionsDb = collectionsSublevel(db);

    for await (const [key, value] of collectionsDb.iterator()) {
      // Ensure the value is valid and has required properties
      if (value && value.id && value.name) {
        collections.push({
          ...value,
          gameIds: value.gameIds || [], // Ensure gameIds is always an array
          createdAt: value.createdAt || new Date(),
          updatedAt: value.updatedAt || new Date(),
        });
      }
    }

    // Sort by creation date (newest first)
    return collections.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  } catch (error) {
    console.error("Failed to get collections:", error);
    return [];
  }
};

registerEvent("getCollections", getCollections);
