import { useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { useOptimizedSteamNews } from '@renderer/hooks/use-optimized-steam-news';
import { useLibrary } from '@renderer/hooks';
import NewsCards from './news-cards';

import './library-news.scss';

interface OptimizedLibraryNewsProps {
  isVisible?: boolean;
}

/**
 * Optimized version of LibraryNews with better performance and caching
 */
export function OptimizedLibraryNews({ isVisible = true }: OptimizedLibraryNewsProps) {
  const { t } = useTranslation('library');
  const { library } = useLibrary();
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const [isIntersecting, setIsIntersecting] = useState(false);
  const containerRef = useRef<HTMLElement>(null);

  // Enhanced Intersection Observer for better lazy loading
  useEffect(() => {
    if (!isVisible || hasBeenVisible) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsIntersecting(entry.isIntersecting);
          if (entry.isIntersecting) {
            setHasBeenVisible(true);
          }
        });
      },
      { 
        threshold: 0.1,
        rootMargin: '50px' // Start loading 50px before component is visible
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible, hasBeenVisible]);

  // Optimized Steam games processing with better memoization
  const steamGamesData = useMemo(() => {
    if (!library.length) {
      return { 
        appIds: [], 
        gameMap: new Map<number, string>(),
        hasGames: false 
      };
    }

    const steamGames = library.filter(game => 
      game.shop === 'steam' && 
      game.objectId && 
      !isNaN(parseInt(game.objectId))
    );

    if (!steamGames.length) {
      return { 
        appIds: [], 
        gameMap: new Map<number, string>(),
        hasGames: false 
      };
    }

    const appIds = steamGames.map(game => parseInt(game.objectId));
    const gameMap = new Map(
      steamGames.map(game => [parseInt(game.objectId), game.title])
    );

    return { appIds, gameMap, hasGames: true };
  }, [library]);

  // Only fetch news when component is visible and we have Steam games
  const shouldFetchNews = hasBeenVisible && isVisible && steamGamesData.hasGames;

  const { 
    news, 
    isLoading, 
    isFetching, 
    error, 
    refresh, 
    isStale 
  } = useOptimizedSteamNews(steamGamesData.appIds, {
    enabled: shouldFetchNews,
    maxItemsPerGame: 3,
    refreshInterval: 20 * 60 * 1000, // 20 minutes
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 60 * 60 * 1000, // 1 hour
  });

  // Enhanced news items with optimized game title lookup
  const enhancedNews = useMemo(() => {
    if (!news.length || !steamGamesData.gameMap.size) return [];
    
    return news.map(item => ({
      ...item,
      gameTitle: steamGamesData.gameMap.get(item.appid) || item.feedname
    }));
  }, [news, steamGamesData.gameMap]);

  // Memoized refresh handler
  const handleRefresh = useCallback(async () => {
    await refresh();
  }, [refresh]);

  // Early returns for better performance
  if (!isVisible || !steamGamesData.hasGames) {
    return null;
  }

  if (error) {
    return (
      <section ref={containerRef} className="library-news">
        <div className="library-news__error">
          <p>{t('news_error')}: {error}</p>
          <button 
            onClick={handleRefresh}
            className="library-news__retry-button"
            disabled={isFetching}
          >
            {isFetching ? t('loading_news') : t('refresh')}
          </button>
        </div>
      </section>
    );
  }

  // Show optimized placeholder until component becomes visible
  if (!hasBeenVisible) {
    return (
      <section ref={containerRef} className="library-news">
        <div className="library-news__placeholder">
          <div className="library-news__placeholder-content">
            <h2>{t('whats_new')}</h2>
            <p>{t('loading_news')}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={containerRef} className="library-news">
      <div className="library-news__header">
        {isFetching && !isLoading && (
          <div className="library-news__refresh-indicator">
            <span className="library-news__refresh-dot"></span>
            <span className="library-news__refresh-text">
              {t('refresh')}...
            </span>
          </div>
        )}
        {isStale && !isFetching && (
          <button 
            onClick={handleRefresh}
            className="library-news__refresh-button"
            title={t('refresh')}
          >
            ↻
          </button>
        )}
      </div>
      
      <NewsCards
        newsItems={enhancedNews}
        isLoading={isLoading}
      />
    </section>
  );
}

export default OptimizedLibraryNews;
