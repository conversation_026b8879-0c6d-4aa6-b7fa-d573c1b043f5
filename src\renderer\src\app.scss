@use "./scss/globals.scss";

* {
  box-sizing: border-box;
}

::-webkit-scrollbar {
  width: 9px;
  background-color: globals.$dark-background-color;
}

::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.03);
}

::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 24px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.16);
}

html,
body,
#root,
main {
  height: 100%;
}

body {
  overflow: hidden;
  user-select: none;
  font-family:
    Noto Sans,
    sans-serif;
  font-size: globals.$body-font-size;
  color: globals.$body-color;
  margin: 0;
}

button {
  padding: 0;
  background-color: transparent;
  border: none;
  font-family: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}

p {
  line-height: 20px;
}

#root,
main {
  display: flex;
}

#root {
  flex-direction: column;
}

main {
  overflow: hidden;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

label {
  font-size: globals.$body-font-size;
}

img {
  -webkit-user-drag: none;
}

progress[value] {
  -webkit-appearance: none;
}

.container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  container-name: globals.$app-container;
  container-type: inline-size;

  &__content {
    overflow-y: auto;
    align-items: center;
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
    background: linear-gradient(
      0deg,
      globals.$dark-background-color 50%,
      globals.$background-color 100%
    );
  }
}

.title-bar {
  display: flex;
  width: 100%;
  height: 35px;
  min-height: 35px;
  background-color: globals.$dark-background-color;
  align-items: center;
  padding: 0 calc(globals.$spacing-unit * 2);
  -webkit-app-region: drag;
  z-index: 4;
  border-bottom: 1px solid globals.$border-color;

  &__cloud-text {
    background: linear-gradient(270deg, #16b195 50%, #3e62c0 100%);
    background-clip: text;
    color: transparent;
  }
}
