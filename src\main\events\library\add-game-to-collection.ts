import { collectionsSublevel, levelKeys, db } from "@main/level";

import { registerEvent } from "../register-event";

const addGameToCollection = async (
  _event: Electron.IpcMainInvokeEvent,
  collectionId: string,
  gameId: string
): Promise<void> => {
  try {
    const collectionsDb = collectionsSublevel(db);
    const collectionKey = levelKeys.collection(collectionId);

    // Get existing collection
    const collection = await collectionsDb.get(collectionKey);

    // Ensure gameIds is an array
    const gameIds = collection.gameIds || [];

    // Check if game is already in collection
    if (gameIds.includes(gameId)) {
      return; // Game already in collection, no need to add
    }

    // Add game to collection
    const updatedCollection = {
      ...collection,
      gameIds: [...gameIds, gameId],
      updatedAt: new Date(),
    };
    
    await collectionsDb.put(collectionKey, updatedCollection);
  } catch (error) {
    console.error("Failed to add game to collection:", error);
    throw new Error("Failed to add game to collection");
  }
};

registerEvent("addGameToCollection", addGameToCollection);
