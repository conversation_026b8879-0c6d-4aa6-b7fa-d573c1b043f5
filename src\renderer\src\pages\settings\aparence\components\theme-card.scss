@use "../../../../scss/globals.scss";

.theme-card {
  width: 100%;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  background-color: rgba(globals.$border-color, 0.01);
  border: 1px solid globals.$border-color;
  border-radius: 12px;
  gap: 4px;
  transition: background-color 0.2s ease;
  padding: 16px;
  position: relative;

  &--active {
    background-color: rgba(globals.$border-color, 0.04);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    gap: 16px;

    &__title {
      font-size: 18px;
      font-weight: 600;
      color: globals.$muted-color;
      text-transform: capitalize;
    }

    &__colors {
      display: flex;
      flex-direction: row;
      gap: 8px;

      &__color {
        width: 16px;
        height: 16px;
        border-radius: 4px;
        border: 1px solid globals.$border-color;
      }
    }
  }

  &__author {
    font-size: 12px;
    color: globals.$body-color;
    font-weight: 400;

    &__name {
      font-weight: 600;
      color: rgba(globals.$muted-color, 0.8);
      margin-left: 4px;

      &:hover {
        color: globals.$muted-color;
        cursor: pointer;
        text-decoration: underline;
        text-underline-offset: 2px;
      }
    }
  }

  &__actions {
    display: flex;
    flex-direction: row;
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    gap: 8px;
    justify-content: space-between;

    &__left {
      display: flex;
      flex-direction: row;
      gap: 8px;
    }

    &__right {
      display: flex;
      flex-direction: row;
      gap: 8px;

      &--external {
        display: none;
      }

      Button {
        padding: 8px 11px;
      }
    }
  }
}
