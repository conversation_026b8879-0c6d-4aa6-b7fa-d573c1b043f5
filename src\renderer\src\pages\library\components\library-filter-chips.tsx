import { useTranslation } from "react-i18next";
import { 
  XIcon, 
  CheckIcon, 
  DownloadIcon, 
  TagIcon,
  SortAscIcon,
  SortDescIcon
} from "@primer/octicons-react";

import type { LibraryFilters, LibrarySortBy } from "@types";
import { getSortTranslationKey } from "@renderer/utils/library-sort-utils";

import "./library-filter-chips.scss";

interface LibraryFilterChipsProps {
  filters: LibraryFilters;
  onUpdateFilters: (filters: Partial<LibraryFilters>) => void;
  onClearAll: () => void;
  className?: string;
}

export function LibraryFilterChips({ 
  filters, 
  onUpdateFilters, 
  onClearAll, 
  className 
}: LibraryFilterChipsProps) {
  const { t } = useTranslation("library");

  const hasActiveFilters = 
    filters.genres.length > 0 ||
    filters.showInstalledOnly ||
    filters.showNotInstalledOnly ||
    (filters.searchQuery && filters.searchQuery.length > 0) ||
    filters.sortBy !== "name-asc";

  if (!hasActiveFilters) {
    return null;
  }

  const handleRemoveGenre = (genre: string) => {
    onUpdateFilters({
      genres: filters.genres.filter(g => g !== genre)
    });
  };

  const handleRemoveSearch = () => {
    onUpdateFilters({ searchQuery: "" });
  };

  const handleRemoveInstalledFilter = () => {
    onUpdateFilters({ showInstalledOnly: false });
  };

  const handleRemoveNotInstalledFilter = () => {
    onUpdateFilters({ showNotInstalledOnly: false });
  };

  const handleResetSort = () => {
    onUpdateFilters({ sortBy: "name-asc" });
  };

  const getSortIcon = (sortBy: LibrarySortBy) => {
    if (sortBy.endsWith("-asc")) {
      return <SortAscIcon size={12} />;
    }
    return <SortDescIcon size={12} />;
  };

  return (
    <div className={`library-filter-chips ${className || ""}`}>
      <div className="library-filter-chips__content">
        <div className="library-filter-chips__header">
          <span className="library-filter-chips__label">{t("active_filters")}:</span>
          <span className="library-filter-chips__count">
            {filters.genres.length + 
             (filters.showInstalledOnly ? 1 : 0) + 
             (filters.showNotInstalledOnly ? 1 : 0) + 
             (filters.searchQuery ? 1 : 0) +
             (filters.sortBy !== "name-asc" ? 1 : 0)}
          </span>
        </div>

        <div className="library-filter-chips__chips">
          {/* Search Query Chip */}
          {filters.searchQuery && (
            <div className="library-filter-chips__chip library-filter-chips__chip--search">
              <TagIcon size={12} />
              <span className="library-filter-chips__chip-text">
                "{filters.searchQuery}"
              </span>
              <button
                type="button"
                className="library-filter-chips__chip-remove"
                onClick={handleRemoveSearch}
                title={t("remove_filter")}
              >
                <XIcon size={10} />
              </button>
            </div>
          )}

          {/* Sort Chip */}
          {filters.sortBy !== "name-asc" && (
            <div className="library-filter-chips__chip library-filter-chips__chip--sort">
              {getSortIcon(filters.sortBy)}
              <span className="library-filter-chips__chip-text">
                {t(getSortTranslationKey(filters.sortBy))}
              </span>
              <button
                type="button"
                className="library-filter-chips__chip-remove"
                onClick={handleResetSort}
                title={t("remove_filter")}
              >
                <XIcon size={10} />
              </button>
            </div>
          )}

          {/* Installed Only Chip */}
          {filters.showInstalledOnly && (
            <div className="library-filter-chips__chip library-filter-chips__chip--status">
              <CheckIcon size={12} />
              <span className="library-filter-chips__chip-text">
                {t("installed_only")}
              </span>
              <button
                type="button"
                className="library-filter-chips__chip-remove"
                onClick={handleRemoveInstalledFilter}
                title={t("remove_filter")}
              >
                <XIcon size={10} />
              </button>
            </div>
          )}

          {/* Not Installed Only Chip */}
          {filters.showNotInstalledOnly && (
            <div className="library-filter-chips__chip library-filter-chips__chip--status">
              <DownloadIcon size={12} />
              <span className="library-filter-chips__chip-text">
                {t("not_installed_only")}
              </span>
              <button
                type="button"
                className="library-filter-chips__chip-remove"
                onClick={handleRemoveNotInstalledFilter}
                title={t("remove_filter")}
              >
                <XIcon size={10} />
              </button>
            </div>
          )}

          {/* Genre Chips */}
          {filters.genres.map((genre) => (
            <div 
              key={genre} 
              className="library-filter-chips__chip library-filter-chips__chip--genre"
            >
              <TagIcon size={12} />
              <span className="library-filter-chips__chip-text">{genre}</span>
              <button
                type="button"
                className="library-filter-chips__chip-remove"
                onClick={() => handleRemoveGenre(genre)}
                title={t("remove_filter")}
              >
                <XIcon size={10} />
              </button>
            </div>
          ))}
        </div>

        {/* Clear All Button */}
        <button
          type="button"
          className="library-filter-chips__clear-all"
          onClick={onClearAll}
        >
          <XIcon size={14} />
          <span>{t("clear_all")}</span>
        </button>
      </div>
    </div>
  );
}
