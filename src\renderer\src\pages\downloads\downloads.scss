@use "../../scss/globals.scss";

.downloads {
  &__container {
    display: flex;
    padding: calc(globals.$spacing-unit * 3);
    flex-direction: column;
    width: 100%;
  }

  &__groups {
    display: flex;
    gap: calc(globals.$spacing-unit * 3);
    flex-direction: column;
  }

  &__arrow-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.06);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: calc(globals.$spacing-unit * 2);
  }

  &__no-downloads {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: globals.$spacing-unit;
  }
}
