import { useState, useEffect, useCallback } from 'react';
import { steamNewsService, SteamNewsItem } from '@renderer/services/steam-news-service';
import { useTranslation } from 'react-i18next';
import { getSteamLanguage } from '@renderer/helpers';

interface UseSteamNewsOptions {
  enabled?: boolean;
  maxItemsPerGame?: number;
  refreshInterval?: number; // in milliseconds
}

interface UseSteamNewsReturn {
  news: SteamNewsItem[];
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  clearCache: () => void;
}

export function useSteamNews(
  appIds: number[],
  options: UseSteamNewsOptions = {}
): UseSteamNewsReturn {
  const {
    enabled = true,
    maxItemsPerGame = 2,
    refreshInterval
  } = options;

  const { i18n } = useTranslation();
  const [news, setNews] = useState<SteamNewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Using centralized getSteamLanguage function from helpers

  const fetchNews = useCallback(async (forceRefresh = false) => {
    if (!enabled || appIds.length === 0) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const steamLanguage = getSteamLanguage(i18n.language);
      console.log(`🌍 CURRENT APP LANGUAGE: ${i18n.language} -> ${steamLanguage}`);
      console.log(`📡 Fetching RSS news for ${appIds.length} games`);

      // Only clear cache if explicitly requested (force refresh)
      if (forceRefresh) {
        console.log('🗑️ Force refresh: clearing cache');
        steamNewsService.clearCache();
      }

      const newsItems = await steamNewsService.getLibraryNews(appIds, maxItemsPerGame, steamLanguage);
      setNews(newsItems);

      // Analyze RSS vs API usage
      const rssCount = newsItems.filter(item => item.feedname === 'steam_community_rss').length;
      const apiCount = newsItems.length - rssCount;

      console.log(`📰 RSS News Summary: ${newsItems.length} total (${rssCount} RSS, ${apiCount} API fallback)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch news';
      setError(errorMessage);
      console.error('Steam news fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [appIds, enabled, maxItemsPerGame, i18n.language]);

  const refresh = useCallback(async () => {
    await fetchNews(true); // Force refresh when manually triggered
  }, [fetchNews]);

  const clearCache = useCallback(() => {
    steamNewsService.clearCache();
    setNews([]);
  }, []);

  // Initial fetch - use cache if available
  useEffect(() => {
    fetchNews(false);
  }, [fetchNews]);

  // Auto-refresh interval - use cache-respecting fetch
  useEffect(() => {
    if (!refreshInterval || !enabled) {
      return;
    }

    const interval = setInterval(() => fetchNews(false), refreshInterval);
    return () => clearInterval(interval);
  }, [fetchNews, refreshInterval, enabled]);

  return {
    news,
    isLoading,
    error,
    refresh,
    clearCache
  };
}

interface UseGameNewsReturn {
  news: SteamNewsItem[];
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export function useGameNews(
  appId: number,
  count: number = 3,
  enabled: boolean = true
): UseGameNewsReturn {
  const { i18n } = useTranslation();
  const [news, setNews] = useState<SteamNewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Using centralized getSteamLanguage function from helpers

  const fetchNews = useCallback(async () => {
    if (!enabled || !appId) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const steamLanguage = getSteamLanguage(i18n.language);
      const newsItems = await steamNewsService.getGameNews(appId, count, steamLanguage);
      setNews(newsItems);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch news';
      setError(errorMessage);
      console.error('Game news fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [appId, count, enabled, i18n.language]);

  const refresh = useCallback(async () => {
    await fetchNews();
  }, [fetchNews]);

  useEffect(() => {
    fetchNews();
  }, [fetchNews]);

  return {
    news,
    isLoading,
    error,
    refresh
  };
}
