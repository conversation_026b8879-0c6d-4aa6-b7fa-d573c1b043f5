import { collectionsSublevel, levelKeys, db } from "@main/level";

import { registerEvent } from "../register-event";

const deleteCollection = async (
  _event: Electron.IpcMainInvokeEvent,
  collectionId: string
): Promise<void> => {
  try {
    const collectionsDb = collectionsSublevel(db);
    const collectionKey = levelKeys.collection(collectionId);

    // Check if collection exists
    const existingCollection = await collectionsDb.get(collectionKey).catch(() => null);
    if (!existingCollection) {
      throw new Error("Collection not found");
    }

    await collectionsDb.del(collectionKey);
  } catch (error) {
    console.error("Failed to delete collection:", error);
    throw new Error("Failed to delete collection");
  }
};

registerEvent("deleteCollection", deleteCollection);
