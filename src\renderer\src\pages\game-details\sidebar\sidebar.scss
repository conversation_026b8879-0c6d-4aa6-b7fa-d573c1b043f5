@use "../../../scss/globals.scss";

.content-sidebar {
  border-left: solid 1px globals.$border-color;
  background-color: globals.$dark-background-color;
  width: 100%;
  height: 100%;

  @media (min-width: 1024px) {
    max-width: 300px;
    width: 100%;
  }

  @media (min-width: 1280px) {
    width: 100%;
    max-width: 400px;
  }
}

.requirement {
  &__button-container {
    width: 100%;
    display: flex;
  }

  &__button {
    border: solid 1px globals.$border-color;
    border-left: none;
    border-right: none;
    border-radius: 0;
    width: 100%;
  }

  &__details {
    padding: calc(globals.$spacing-unit * 2);
    line-height: 22px;
    font-size: globals.$body-font-size;

    a {
      display: flex;
      color: globals.$body-color;
    }
  }

  &__details-skeleton {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
    padding: calc(globals.$spacing-unit * 2);
    font-size: globals.$body-font-size;
  }
}

.how-long-to-beat {
  &__categories-list {
    margin: 0;
    padding: calc(globals.$spacing-unit * 2);
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__category {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 2);
    background: linear-gradient(
      90deg,
      transparent 20%,
      rgb(255 255 255 / 2%) 100%
    );
    border-radius: 4px;
    padding: globals.$spacing-unit calc(globals.$spacing-unit * 2);
    border: solid 1px globals.$border-color;
  }

  &__category-label {
    color: globals.$muted-color;
  }

  &__category-label--bold {
    font-weight: bold;
  }

  &__category-skeleton {
    border: solid 1px globals.$border-color;
    border-radius: 4px;
    height: 76px;
  }
}

.stats {
  &__section {
    display: flex;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2);
    justify-content: space-between;
    transition: max-height ease 0.5s;
    overflow: hidden;

    @media (min-width: 1024px) {
      flex-direction: column;
    }

    @media (min-width: 1280px) {
      flex-direction: row;
    }
  }

  &__category-title {
    font-size: globals.$small-font-size;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: globals.$spacing-unit;
  }

  &__category {
    display: flex;
    flex-direction: row;
    gap: calc(globals.$spacing-unit / 2);
    justify-content: space-between;
    align-items: center;
  }
}

.list {
  list-style: none;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: calc(globals.$spacing-unit * 2);
  padding: calc(globals.$spacing-unit * 2);

  &__item {
    display: flex;
    cursor: pointer;
    transition: all ease 0.1s;
    color: globals.$muted-color;
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
    padding: globals.$spacing-unit;
    gap: calc(globals.$spacing-unit * 2);
    align-items: center;
    text-align: left;

    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
      text-decoration: none;
    }
  }

  &__item-image {
    width: 54px;
    height: 54px;
    border-radius: 4px;
    object-fit: cover;

    &--locked {
      filter: grayscale(100%);
    }
  }
}

.subscription-required-button {
  text-decoration: none;
  display: flex;
  justify-content: center;
  width: 100%;
  gap: calc(globals.$spacing-unit / 2);
  color: globals.$warning-color;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.achievements-placeholder {
  position: absolute;
  z-index: 1;
  inset: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 8px;
}

.achievements-placeholder__blur {
  filter: blur(4px);
}
