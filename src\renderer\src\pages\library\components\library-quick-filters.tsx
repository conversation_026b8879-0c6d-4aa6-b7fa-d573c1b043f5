import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  DownloadIcon,
  XIcon,
  ClockIcon,
  StarIcon,
  PlayIcon,
  TagIcon,
  SortAscIcon,
} from "@primer/octicons-react";

import type { LibraryFilters } from "@types";
import { getSortTranslationKey } from "@renderer/utils/library-sort-utils";

import "./library-quick-filters.scss";

interface LibraryQuickFiltersProps {
  filters: LibraryFilters;
  onUpdateFilters: (filters: Partial<LibraryFilters>) => void;
  onClearAll: () => void;
  className?: string;
}

interface QuickFilter {
  id: string;
  label: string;
  icon: React.ReactNode;
  isActive: boolean;
  onClick: () => void;
  color?: string;
}

export function LibraryQuickFilters({
  filters,
  onUpdateFilters,
  onClearAll,
  className,
}: LibraryQuickFiltersProps) {
  const { t } = useTranslation("library");

  const handleInstalledToggle = useCallback(() => {
    onUpdateFilters({
      showInstalledOnly: !filters.showInstalledOnly,
      showNotInstalledOnly: false,
    });
  }, [filters.showInstalledOnly, onUpdateFilters]);

  const handleNotInstalledToggle = useCallback(() => {
    onUpdateFilters({
      showNotInstalledOnly: !filters.showNotInstalledOnly,
      showInstalledOnly: false,
    });
  }, [filters.showNotInstalledOnly, onUpdateFilters]);

  const handleClearSearch = useCallback(() => {
    onUpdateFilters({ searchQuery: "" });
  }, [onUpdateFilters]);

  const handleRemoveGenre = useCallback((genre: string) => {
    onUpdateFilters({
      genres: filters.genres.filter(g => g !== genre)
    });
  }, [filters.genres, onUpdateFilters]);

  const handleSortReset = useCallback(() => {
    onUpdateFilters({ sortBy: "name-asc" });
  }, [onUpdateFilters]);

  // Define quick filter buttons
  const quickFilters: QuickFilter[] = [
    {
      id: "installed",
      label: t("installed_only"),
      icon: <DownloadIcon size={14} />,
      isActive: filters.showInstalledOnly,
      onClick: handleInstalledToggle,
      color: "#1c9749",
    },
    {
      id: "not-installed",
      label: t("not_installed_only"),
      icon: <XIcon size={14} />,
      isActive: filters.showNotInstalledOnly,
      onClick: handleNotInstalledToggle,
      color: "#801d1e",
    },
  ];

  // Check if we have any active filters
  const hasActiveFilters = 
    filters.genres.length > 0 ||
    filters.showInstalledOnly ||
    filters.showNotInstalledOnly ||
    (filters.searchQuery && filters.searchQuery.length > 0) ||
    filters.sortBy !== "name-asc";

  const hasActiveQuickFilters = quickFilters.some(filter => filter.isActive);

  if (!hasActiveFilters && !hasActiveQuickFilters) {
    return null;
  }

  return (
    <div className={`library-quick-filters ${className || ""}`}>
      <div className="library-quick-filters__content">
        {/* Active Search Query */}
        {filters.searchQuery && filters.searchQuery.length > 0 && (
          <div className="library-quick-filters__chip library-quick-filters__chip--search">
            <span className="library-quick-filters__chip-label">
              {t("search")}: "{filters.searchQuery}"
            </span>
            <button
              type="button"
              className="library-quick-filters__chip-remove"
              onClick={handleClearSearch}
              title={t("clear_search")}
            >
              <XIcon size={12} />
            </button>
          </div>
        )}

        {/* Active Quick Filters */}
        {quickFilters.map(filter => 
          filter.isActive && (
            <div
              key={filter.id}
              className="library-quick-filters__chip library-quick-filters__chip--filter"
              style={{ borderColor: filter.color, backgroundColor: `${filter.color}20` }}
            >
              <div className="library-quick-filters__chip-icon" style={{ color: filter.color }}>
                {filter.icon}
              </div>
              <span className="library-quick-filters__chip-label">{filter.label}</span>
              <button
                type="button"
                className="library-quick-filters__chip-remove"
                onClick={filter.onClick}
                title={t("remove_filter")}
              >
                <XIcon size={12} />
              </button>
            </div>
          )
        )}

        {/* Active Genres */}
        {filters.genres.map(genre => (
          <div
            key={genre}
            className="library-quick-filters__chip library-quick-filters__chip--genre"
          >
            <div className="library-quick-filters__chip-icon">
              <TagIcon size={12} />
            </div>
            <span className="library-quick-filters__chip-label">{genre}</span>
            <button
              type="button"
              className="library-quick-filters__chip-remove"
              onClick={() => handleRemoveGenre(genre)}
              title={t("remove_genre")}
            >
              <XIcon size={12} />
            </button>
          </div>
        ))}

        {/* Active Sort (if not default) */}
        {filters.sortBy !== "name-asc" && (
          <div className="library-quick-filters__chip library-quick-filters__chip--sort">
            <div className="library-quick-filters__chip-icon">
              <SortAscIcon size={12} />
            </div>
            <span className="library-quick-filters__chip-label">
              {t("sorted_by")}: {t(getSortTranslationKey(filters.sortBy))}
            </span>
            <button
              type="button"
              className="library-quick-filters__chip-remove"
              onClick={handleSortReset}
              title={t("reset_sort")}
            >
              <XIcon size={12} />
            </button>
          </div>
        )}

        {/* Clear All Button */}
        {hasActiveFilters && (
          <button
            type="button"
            className="library-quick-filters__clear-all"
            onClick={onClearAll}
          >
            <XIcon size={14} />
            {t("clear_all_filters")}
          </button>
        )}
      </div>
    </div>
  );
}


