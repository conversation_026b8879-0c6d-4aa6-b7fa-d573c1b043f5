@use "../../scss/globals.scss";

.backdrop {
  animation-name: backdrop-fade-in;
  animation-duration: 0.4s;
  background-color: rgba(0, 0, 0, 0.7);
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: globals.$backdrop-z-index;
  top: 0;
  padding: calc(globals.$spacing-unit * 3);
  backdrop-filter: blur(2px);
  transition: all ease 0.2s;

  &--closing {
    animation-name: backdrop-fade-out;
    backdrop-filter: blur(0px);
    background-color: rgba(0, 0, 0, 0);
  }

  &--windows {
    padding-top: calc(#{globals.$spacing-unit * 3} + 35);
  }
}

@keyframes backdrop-fade-in {
  0% {
    backdrop-filter: blur(0px);
    background-color: rgba(0, 0, 0, 0.5);
  }
  100% {
    backdrop-filter: blur(2px);
    background-color: rgba(0, 0, 0, 0.7);
  }
}

@keyframes backdrop-fade-out {
  0% {
    backdrop-filter: blur(2px);
    background-color: rgba(0, 0, 0, 0.7);
  }
  100% {
    backdrop-filter: blur(0px);
    background-color: rgba(0, 0, 0, 0);
  }
}
