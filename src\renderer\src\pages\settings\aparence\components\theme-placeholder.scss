@use "../../../../scss/globals.scss";

.theme-placeholder {
  width: 100%;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 24px;
  background-color: rgba(globals.$border-color, 0.01);
  cursor: pointer;
  border: 1px dashed globals.$border-color;
  border-radius: 12px;
  gap: 12px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(globals.$border-color, 0.03);
  }

  &__icon {
    svg {
      width: 32px;
      height: 32px;
      color: globals.$body-color;
      opacity: 0.7;
    }
  }

  &__text {
    text-align: center;
    max-width: 400px;
    font-size: 14.5px;
    line-height: 1.6;
    font-weight: 400;
    color: rgba(globals.$body-color, 0.85);
  }
}
