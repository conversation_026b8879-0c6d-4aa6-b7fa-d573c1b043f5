import { NEWS_PERFORMANCE_CONFIG, performanceTracker } from '@renderer/config/news-performance';

export interface SteamNewsItem {
  gid: string;
  title: string;
  url: string;
  is_external_url: boolean;
  author: string;
  contents: string;
  feedlabel: string;
  date: number;
  feedname: string;
  feed_type: number;
  appid: number;
  tags?: string[];
  // Enhanced fields
  image?: string;
  gameTitle?: string;
  isEvent?: boolean;
  eventDate?: number;
  summary?: string;
  processedContent?: string;
  thumbnailUrl?: string;
  detectedLanguage?: string;
  contentLength?: number;
  hasImages?: boolean;
}

export interface SteamNewsResponse {
  appnews: {
    appid: number;
    newsitems: SteamNewsItem[];
    count: number;
  };
}

export interface GameNewsCache {
  [appId: string]: {
    news: SteamNewsItem[];
    lastFetched: number;
    expiresAt: number;
    language?: string;
  };
}

export interface LanguageConfig {
  code: string;
  steamCode: string;
  name: string;
  commonWords: string[];
  characterPattern?: RegExp;
  threshold: number;
}

class SteamNewsService {
  private cache: GameNewsCache = {};
  private readonly CACHE_DURATION = NEWS_PERFORMANCE_CONFIG.CACHE_DURATION;
  private readonly RSS_BASE = 'https://steamcommunity.com/games/';
  private readonly API_BASE = 'https://api.steampowered.com/ISteamNews/GetNewsForApp/v0002/'; // Fallback only
  private readonly RATE_LIMIT_DELAY = NEWS_PERFORMANCE_CONFIG.RATE_LIMIT_DELAY;
  private readonly MAX_CONCURRENT_REQUESTS = NEWS_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS;
  private lastRequestTime = 0;
  private activeRequests = 0;
  private requestQueue: Array<() => Promise<void>> = [];
  private isProcessingQueue = false;

  // Comprehensive language configuration
  private readonly LANGUAGES: LanguageConfig[] = [
    {
      code: 'en',
      steamCode: 'english',
      name: 'English',
      commonWords: ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'],
      threshold: 0.15
    },
    {
      code: 'es',
      steamCode: 'spanish',
      name: 'Español',
      commonWords: ['que', 'de', 'no', 'a', 'la', 'el', 'es', 'y', 'en', 'lo', 'un', 'por', 'qué', 'me', 'una', 'te', 'los', 'se', 'con', 'para', 'mi', 'está', 'si', 'bien', 'pero', 'yo', 'eso', 'las', 'sí', 'su', 'tu', 'aquí', 'del', 'al', 'como', 'le', 'más', 'esto', 'ya', 'todo', 'esta', 'vamos', 'muy', 'hay', 'ahora', 'algo', 'estoy', 'tengo', 'nos', 'tú', 'nada', 'cuando', 'ha', 'este', 'sé', 'estás', 'así', 'puedo', 'cómo', 'quiero', 'solo', 'soy', 'tiene', 'nos', 'ni', 'donde', 'él', 'ella', 'estar', 'tenía', 'lo', 'todo'],
      threshold: 0.12
    },
    {
      code: 'de',
      steamCode: 'german',
      name: 'Deutsch',
      commonWords: ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich', 'des', 'auf', 'für', 'ist', 'im', 'dem', 'nicht', 'ein', 'eine', 'als', 'auch', 'es', 'an', 'werden', 'aus', 'er', 'hat', 'dass', 'sie', 'nach', 'wird', 'bei', 'einer', 'um', 'am', 'sind', 'noch', 'wie', 'einem', 'über', 'einen', 'so', 'zum', 'war', 'haben', 'nur', 'oder', 'aber', 'vor', 'zur', 'bis', 'mehr', 'durch', 'man', 'sein', 'wurde', 'sei', 'ich', 'diese', 'einem', 'dieser', 'wieder', 'wenn', 'können', 'schon', 'alle', 'ihre', 'machen', 'dann', 'unter', 'kommen', 'sehr', 'was', 'sagen', 'hier', 'geben', 'werden', 'müssen', 'gehen', 'wissen', 'mein', 'leben', 'zeit', 'arbeiten', 'jahr', 'kein', 'mensch', 'tag', 'gut', 'neu', 'welt', 'sehen', 'lassen', 'teil', 'eigen', 'hand', 'stehen', 'jung', 'woche', 'land', 'halten', 'ohne', 'liegen', 'deutsch', 'ganz', 'dabei', 'helfen', 'natürlich', 'ja', 'doch', 'mal', 'zwei', 'heute', 'ersten', 'deutschen', 'ihm', 'wollen', 'großen', 'deutschen', 'erste', 'meine', 'deutsche', 'heißen', 'system', 'jede', 'unsere', 'deutschen', 'deren', 'während', 'fall', 'bis', 'gruppe', 'problem', 'gegen', 'gegenüber', 'gestellt', 'groß', 'teil', 'nehmen', 'spielen', 'zahl', 'gesellschaft', 'heute', 'information', 'nichts', 'geschichte', 'frage', 'recht', 'neue', 'stelle', 'art', 'person', 'politik', 'ende', 'einzelnen', 'sicher', 'außerdem', 'weit', 'entwicklung', 'prozent', 'zusammen', 'jahren', 'menschen', 'weise', 'kleine', 'staat', 'seite', 'seit', 'ohne', 'solche', 'sollte'],
      threshold: 0.12
    },
    {
      code: 'fr',
      steamCode: 'french',
      name: 'Français',
      commonWords: ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'une', 'être', 'et', 'à', 'il', 'avoir', 'ne', 'je', 'son', 'que', 'se', 'qui', 'ce', 'dans', 'en', 'du', 'elle', 'au', 'de', 'le', 'tout', 'et', 'y', 'mais', 'd', 'lui', 'nous', 'comme', 'ou', 'si', 'leur', 'temps', 'très', 'me', 'personne', 'année', 'mon', 'même', 'et', 'où', 'quelque', 'être', 'ayant', 'de', 'pour', 'que', 'dans', 'ce', 'il', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'une', 'être', 'et', 'à', 'il', 'avoir', 'ne', 'je', 'son', 'que', 'se', 'qui', 'ce', 'dans', 'en', 'du', 'elle', 'au', 'de', 'le', 'tout', 'et', 'y', 'mais', 'd', 'lui', 'nous', 'comme', 'ou', 'si', 'leur', 'temps', 'très', 'me', 'personne', 'année', 'mon', 'même', 'et', 'où', 'quelque', 'être', 'ayant'],
      threshold: 0.12
    },
    {
      code: 'ru',
      steamCode: 'russian',
      name: 'Русский',
      commonWords: ['в', 'и', 'не', 'на', 'я', 'быть', 'с', 'а', 'то', 'все', 'она', 'так', 'его', 'но', 'да', 'ты', 'к', 'у', 'же', 'вы', 'за', 'бы', 'по', 'только', 'ее', 'мне', 'было', 'вот', 'от', 'меня', 'еще', 'нет', 'о', 'из', 'ему', 'теперь', 'когда', 'даже', 'ну', 'вдруг', 'ли', 'если', 'уже', 'или', 'ни', 'быть', 'был', 'него', 'до', 'вас', 'нибудь', 'опять', 'уж', 'вам', 'ведь', 'там', 'потом', 'себя', 'ничего', 'ей', 'может', 'они', 'тут', 'где', 'есть', 'надо', 'ней', 'для', 'мы', 'тебя', 'их', 'чем', 'была', 'сам', 'чтоб', 'без', 'будто', 'чего', 'раз', 'тоже', 'себе', 'под', 'будет', 'ж', 'тогда', 'кто', 'этот', 'того', 'потому', 'этого', 'какой', 'совсем', 'ним', 'здесь', 'этом', 'один', 'почти', 'мой', 'тем', 'чтобы', 'нее', 'сейчас', 'были', 'куда', 'зачем', 'всех', 'никогда', 'можно', 'при', 'наконец', 'два', 'об', 'другой', 'хоть', 'после', 'над', 'больше', 'тот', 'через', 'эти', 'нас', 'про', 'всего', 'них', 'какая', 'много', 'разве', 'три', 'эту', 'моя', 'впрочем', 'хорошо', 'свою', 'этой', 'перед', 'иногда', 'лучше', 'чуть', 'том', 'нельзя', 'такой', 'им', 'более', 'всегда', 'конечно', 'всю', 'между'],
      characterPattern: /[\u0400-\u04FF]/g,
      threshold: 0.10
    },
    {
      code: 'zh',
      steamCode: 'schinese',
      name: '中文',
      commonWords: ['的', '一', '是', '在', '不', '了', '有', '和', '人', '这', '中', '大', '为', '上', '个', '国', '我', '以', '要', '他', '时', '来', '用', '们', '生', '到', '作', '地', '于', '出', '就', '分', '对', '成', '会', '可', '主', '发', '年', '动', '同', '工', '也', '能', '下', '过', '子', '说', '产', '种', '面', '而', '方', '后', '多', '定', '行', '学', '法', '所', '民', '得', '经', '十', '三', '之', '进', '着', '等', '部', '度', '家', '电', '力', '里', '如', '水', '化', '高', '自', '二', '理', '起', '小', '物', '现', '实', '加', '量', '都', '两', '体', '制', '机', '当', '使', '点', '从', '业', '本', '去', '把', '性', '好', '应', '开', '它', '合', '还', '因', '由', '其', '些', '然', '前', '外', '天', '政', '四', '日', '那', '社', '义', '事', '平', '形', '相', '全', '表', '间', '样', '与', '关', '各', '重', '新', '线', '内', '数', '正', '心', '反', '你', '明', '看', '原', '又', '么', '利', '比', '或', '但', '质', '气', '第', '向', '道', '命', '此', '变', '条', '只', '没', '结', '解', '问', '意', '建', '月', '公', '无', '系', '军', '很', '情', '者', '最', '立', '代', '想', '已', '通', '并', '提', '直', '题', '党', '程', '展', '五', '果', '料', '象', '员', '革', '位', '入', '常', '文', '总', '次', '品', '式', '活', '设', '及', '管', '特', '件', '长', '求', '老', '头', '基', '资', '边', '流', '路', '级', '少', '图', '山', '统', '接', '知', '较', '将', '组', '见', '计', '别', '她', '手', '角', '期', '根', '论', '运', '农', '指', '几', '九', '区', '强', '放', '决', '西', '被', '干', '做', '必', '战', '先', '回', '则', '任', '取', '据', '处', '队', '南', '给', '色', '光', '门', '即', '保', '治', '北', '造', '百', '规', '热', '领', '七', '海', '口', '东', '导', '器', '压', '志', '世', '金', '增', '争', '济', '阶', '油', '思', '术', '极', '交', '受', '联', '什', '认', '六', '共', '权', '收', '证', '改', '清', '美', '再', '采', '转', '更', '单', '风', '切', '打', '白', '教', '速', '花', '带', '安', '场', '身', '车', '例', '真', '务', '具', '万', '每', '目', '至', '达', '走', '积', '示', '议', '声', '报', '斗', '完', '类', '八', '离', '华', '名', '确', '才', '科', '张', '信', '马', '节', '话', '米', '整', '空', '元', '况', '今', '集', '温', '传', '土', '许', '步', '群', '广', '石', '记', '需', '段', '研', '界', '拉', '林', '律', '叫', '且', '究', '观', '越', '织', '装', '影', '算', '低', '持', '音', '众', '书', '布', '复', '容', '儿', '须', '际', '商', '非', '验', '连', '断', '深', '难', '近', '矿', '千', '周', '委', '素', '技', '备', '半', '办', '青', '省', '列', '习', '响', '约', '支', '般', '史', '感', '劳', '便', '团', '往', '酸', '历', '市', '克', '何', '除', '消', '构', '府', '称', '太', '准', '精', '值', '号', '率', '族', '维', '划', '选', '标', '写', '存', '候', '毛', '亲', '快', '效', '斯', '院', '查', '江', '型', '眼', '王', '按', '格', '养', '易', '置', '派', '层', '片', '始', '却', '专', '状', '育', '厂', '京', '识', '适', '属', '圆', '包', '火', '住', '调', '满', '县', '局', '照', '参', '红', '细', '引', '听', '该', '铁', '价', '严', '首', '底', '液', '官', '德', '随', '病', '苏', '失', '尔', '死', '讲', '配', '女', '黄', '推', '显', '谈', '罪', '神', '艺', '呢', '席', '含', '企', '望', '密', '批', '营', '项', '防', '举', '球', '英', '氧', '势', '告', '李', '台', '落', '木', '帮', '轮', '破', '亚', '师', '围', '注', '远', '字', '材', '排', '供', '河', '态', '封', '另', '施', '减', '树', '溶', '怎', '止', '案', '言', '士', '均', '武', '固', '叶', '鱼', '波', '视', '仅', '费', '紧', '爱', '左', '章', '早', '朝', '害', '续', '轻', '服', '试', '食', '充', '兵', '源', '判', '护', '司', '足', '某', '练', '差', '致', '板', '田', '降', '黑', '犯', '负', '击', '范', '继', '兴', '似', '余', '坚', '曲', '输', '修', '故', '城', '夫', '够', '送', '笔', '船', '占', '右', '财', '吃', '富', '春', '职', '觉', '汉', '画', '功', '巴', '跟', '虽', '杂', '飞', '检', '吸', '助', '升', '阳', '互', '初', '创', '抗', '考', '投', '坏', '策', '古', '径', '换', '未', '跑', '留', '钢', '曾', '端', '责', '站', '简', '述', '钱', '副', '尽', '帝', '射', '草', '冲', '承', '独', '令', '限', '阿', '宣', '环', '双', '请', '超', '微', '让', '控', '州', '良', '轴', '找', '否', '纪', '益', '依', '优', '顶', '础', '载', '倒', '房', '突', '坐', '粉', '敌', '略', '客', '袁', '冷', '胜', '绝', '析', '块', '剂', '测', '丝', '协', '诉', '念', '陈', '仍', '罗', '盐', '友', '洋', '错', '苦', '夜', '刑', '移', '频', '逐', '靠', '混', '母', '短', '皮', '终', '聚', '汽', '村', '云', '哪', '既', '距', '卫', '停', '烈', '央', '察', '烧', '迅', '境', '若', '印', '洲', '刻', '括', '激', '孔', '搞', '甚', '室', '待', '核', '校', '散', '侵', '吧', '甲', '游', '久', '菜', '味', '旧', '模', '湖', '货', '损', '预', '阻', '毫', '普', '稳', '乙', '妈', '植', '息', '扩', '银', '语', '挥', '酒', '守', '拿', '序', '纸', '医', '缺', '雨', '吗', '针', '刘', '啊', '急', '唱', '误', '训', '愿', '审', '附', '获', '茶', '鲜', '粮', '斤', '孩', '脱', '硫', '肥', '善', '龙', '演', '父', '渐', '血', '欢', '械', '掌', '歌', '沙', '刚', '攻', '谓', '盾', '讨', '晚', '粒', '乱', '燃', '矛', '乎', '杀', '药', '宁', '鲁', '贵', '钟', '煤', '读', '班', '伯', '香', '介', '迫', '句', '丰', '培', '握', '兰', '担', '弦', '蛋', '沉', '假', '穿', '执', '答', '乐', '谁', '顺', '烟', '缩', '征', '脸', '喜', '松', '脚', '困', '异', '免', '背', '星', '福', '买', '染', '井', '概', '慢', '怕', '磁', '倍', '祖', '皇', '促', '静', '补', '评', '翻', '肉', '践', '尼', '衣', '宽', '扬', '棉', '希', '伤', '操', '垂', '秋', '宜', '氢', '套', '督', '振', '架', '亮', '末', '宪', '庆', '编', '牛', '触', '映', '雷', '销', '诗', '座', '居', '抓', '裂', '胞', '呼', '娘', '景', '威', '绿', '晶', '厚', '盟', '衡', '鸡', '孙', '延', '危', '胶', '屋', '乡', '临', '陆', '顾', '掉', '呀', '灯', '岁', '措', '束', '刀', '恶', '停', '育', '届', '属', '圆', '包', '火', '住', '调', '满', '县', '局', '照', '参', '红', '细', '引', '听', '该', '铁', '价', '严', '首', '底', '液', '官', '德', '随', '病', '苏', '失', '尔', '死', '讲', '配', '女', '黄', '推', '显', '谈', '罪', '神', '艺', '呢', '席', '含', '企', '望', '密', '批', '营', '项', '防', '举', '球', '英', '氧', '势', '告', '李', '台', '落', '木', '帮', '轮', '破', '亚', '师', '围', '注', '远', '字', '材', '排', '供', '河', '态', '封', '另', '施', '减', '树', '溶', '怎', '止', '案', '言', '士', '均', '武', '固', '叶', '鱼', '波', '视', '仅', '费', '紧', '爱', '左', '章', '早', '朝', '害', '续', '轻', '服', '试', '食', '充', '兵', '源', '判', '护', '司', '足', '某', '练', '差', '致', '板', '田', '降', '黑', '犯', '负', '击', '范', '继', '兴', '似', '余', '坚', '曲', '输', '修', '故', '城', '夫', '够', '送', '笔', '船', '占', '右', '财', '吃', '富', '春', '职', '觉', '汉', '画', '功', '巴', '跟', '虽', '杂', '飞', '检', '吸', '助', '升', '阳', '互', '初', '创', '抗', '考', '投', '坏', '策', '古', '径', '换', '未', '跑', '留', '钢', '曾', '端', '责', '站', '简', '述', '钱', '副', '尽', '帝', '射', '草', '冲', '承', '独', '令', '限', '阿', '宣', '环', '双', '请', '超', '微', '让', '控', '州', '良', '轴', '找', '否', '纪', '益', '依', '优', '顶', '础', '载', '倒', '房', '突', '坐', '粉', '敌', '略', '客', '袁', '冷', '胜', '绝', '析', '块', '剂', '测', '丝', '协', '诉', '念', '陈', '仍', '罗', '盐', '友', '洋', '错', '苦', '夜', '刑', '移', '频', '逐', '靠', '混', '母', '短', '皮', '终', '聚', '汽', '村', '云', '哪', '既', '距', '卫', '停', '烈', '央', '察', '烧', '迅', '境', '若', '印', '洲', '刻', '括', '激', '孔', '搞', '甚', '室', '待', '核', '校', '散', '侵', '吧', '甲', '游', '久', '菜', '味', '旧', '模', '湖', '货', '损', '预', '阻', '毫', '普', '稳', '乙', '妈', '植', '息', '扩', '银', '语', '挥', '酒', '守', '拿', '序', '纸', '医', '缺', '雨', '吗', '针', '刘', '啊', '急', '唱', '误', '训', '愿', '审', '附', '获', '茶', '鲜', '粮', '斤', '孩', '脱', '硫', '肥', '善', '龙', '演', '父', '渐', '血', '欢', '械', '掌', '歌', '沙', '刚', '攻', '谓', '盾', '讨', '晚', '粒', '乱', '燃', '矛', '乎', '杀', '药', '宁', '鲁', '贵', '钟', '煤', '读', '班', '伯', '香', '介', '迫', '句', '丰', '培', '握', '兰', '担', '弦', '蛋', '沉', '假', '穿', '执', '答', '乐', '谁', '顺', '烟', '缩', '征', '脸', '喜', '松', '脚', '困', '异', '免', '背', '星', '福', '买', '染', '井', '概', '慢', '怕', '磁', '倍', '祖', '皇', '促', '静', '补', '评', '翻', '肉', '践', '尼', '衣', '宽', '扬', '棉', '希', '伤', '操', '垂', '秋', '宜', '氢', '套', '督', '振', '架', '亮', '末', '宪', '庆', '编', '牛', '触', '映', '雷', '销', '诗', '座', '居', '抓', '裂', '胞', '呼', '娘', '景', '威', '绿', '晶', '厚', '盟', '衡', '鸡', '孙', '延', '危', '胶', '屋', '乡', '临', '陆', '顾', '掉', '呀', '灯', '岁', '措', '束', '刀', '恶'],
      characterPattern: /[\u4e00-\u9fff]/g,
      threshold: 0.08
    }
  ];

  /**
   * Get news for a specific game using RSS feeds with enhanced language support
   */
  async getGameNews(appId: number, count: number = 5, language?: string): Promise<SteamNewsItem[]> {
    const normalizedLanguage = this.normalizeLanguage(language);
    const cacheKey = `${appId}_${normalizedLanguage}`;

    console.log(`🎮 Fetching RSS news for app ${appId} in language: ${language} (normalized: ${normalizedLanguage})`);

    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      console.log(`📦 Using cached RSS news for app ${appId}`);
      return this.cache[cacheKey].news;
    }

    try {
      // Rate limiting with concurrent request management
      await this.enforceRateLimit();
      this.activeRequests++;

      let newsItems: SteamNewsItem[] = [];

      // Strategy 1: Try RSS with language parameter (primary approach)
      if (normalizedLanguage !== 'en') {
        console.log(`📡 Trying RSS with language: ${normalizedLanguage}`);
        newsItems = await this.fetchRSSNews(appId, count, normalizedLanguage);
      }

      // Strategy 2: Try general RSS if no localized content
      if (newsItems.length === 0) {
        console.log(`📡 Trying general RSS feed`);
        newsItems = await this.fetchRSSNews(appId, count);
      }

      // Strategy 3: Fallback to API if RSS fails
      if (newsItems.length === 0) {
        console.log(`🔄 RSS failed, falling back to API`);
        newsItems = await this.fetchAPINewsAsFallback(appId, count, normalizedLanguage);
      }

      // Filter and process news items with enhanced language detection
      const filteredItems = this.filterNewsItems(newsItems, normalizedLanguage);
      const processedItems = await this.processNewsItems(filteredItems.slice(0, count), appId);

      // Cache the results
      this.cacheNews(cacheKey, processedItems, normalizedLanguage);

      console.log(`✅ Retrieved ${processedItems.length} RSS news items for app ${appId}`);
      return processedItems;
    } catch (error) {
      console.error(`❌ Failed to fetch RSS news for app ${appId}:`, error);

      // Return cached data if available, even if expired
      if (this.cache[cacheKey]) {
        console.log(`🔄 Using expired cache for app ${appId}`);
        return this.cache[cacheKey].news;
      }

      return [];
    } finally {
      this.activeRequests--;
    }
  }

  /**
   * Fetch news from Steam RSS feeds with language support
   */
  private async fetchRSSNews(appId: number, count: number, language?: string): Promise<SteamNewsItem[]> {
    const rssUrls = this.buildRSSUrls(appId, language);

    for (const rssUrl of rssUrls) {
      try {
        console.log(`📡 Fetching RSS: ${rssUrl}`);

        const response = await fetch(rssUrl, {
          headers: {
            'Accept': 'application/rss+xml, application/xml, text/xml',
            'User-Agent': 'Mozilla/5.0 (compatible; Steam News Reader)',
            ...(language && { 'Accept-Language': `${language},en;q=0.9` })
          }
        });

        if (!response.ok) {
          console.warn(`❌ RSS request failed: ${response.status} for ${rssUrl}`);
          continue;
        }

        const xmlText = await response.text();
        const newsItems = this.parseRSSFeed(xmlText, appId);

        if (newsItems.length > 0) {
          console.log(`✅ RSS success: ${newsItems.length} items from ${rssUrl}`);
          return newsItems.slice(0, count * 2); // Get extra for filtering
        }

        console.log(`⚠️ RSS returned no items: ${rssUrl}`);
      } catch (error) {
        console.warn(`❌ RSS fetch error for ${rssUrl}:`, error);
        continue;
      }
    }

    return [];
  }

  /**
   * Build RSS URLs with language variations
   */
  private buildRSSUrls(appId: number, language?: string): string[] {
    const baseUrl = `${this.RSS_BASE}${appId}/rss/`;
    const urls: string[] = [];

    if (language && language !== 'en') {
      const languageConfig = this.getLanguageConfig(language);
      const steamLanguage = languageConfig?.steamCode || language;

      // Try various language parameter formats
      urls.push(
        `${baseUrl}?l=${language}`,
        `${baseUrl}?l=${steamLanguage}`,
        `${baseUrl}?language=${language}`,
        `${baseUrl}?language=${steamLanguage}`,
        `${baseUrl}?cc=${language.toUpperCase()}`,
        `${baseUrl}?locale=${language}`
      );
    }

    // Always include the base URL as fallback
    urls.push(baseUrl);

    return urls;
  }

  /**
   * Parse RSS XML feed into SteamNewsItem array
   */
  private parseRSSFeed(xmlText: string, appId: number): SteamNewsItem[] {
    const newsItems: SteamNewsItem[] = [];

    try {
      console.log(`🔧 Parsing RSS XML (${xmlText.length} chars)`);

      // Create DOM parser
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

      // Check for parsing errors
      const parserError = xmlDoc.querySelector('parsererror');
      if (parserError) {
        console.error('❌ RSS XML parsing error:', parserError.textContent);
        return newsItems;
      }

      // Extract items from RSS
      const items = xmlDoc.querySelectorAll('item');
      console.log(`📄 Found ${items.length} RSS items`);

      items.forEach((item, index) => {
        try {
          const newsItem = this.parseRSSItem(item, appId, index);
          if (newsItem) {
            newsItems.push(newsItem);
          }
        } catch (error) {
          console.warn(`⚠️ Error parsing RSS item ${index}:`, error);
        }
      });

      console.log(`✅ Successfully parsed ${newsItems.length} RSS news items`);
      return newsItems;
    } catch (error) {
      console.error('❌ RSS parsing failed:', error);
      return newsItems;
    }
  }

  /**
   * Parse individual RSS item into SteamNewsItem
   */
  private parseRSSItem(item: Element, appId: number, index: number): SteamNewsItem | null {
    try {
      // Extract basic RSS fields
      const title = this.getElementText(item, 'title')?.trim();
      const link = this.getElementText(item, 'link')?.trim();
      const description = this.getElementText(item, 'description')?.trim();
      const pubDate = this.getElementText(item, 'pubDate')?.trim();
      const author = this.getElementText(item, 'author')?.trim() ||
                    this.getElementText(item, 'dc:creator')?.trim() ||
                    'Steam Community';
      const guid = this.getElementText(item, 'guid')?.trim() ||
                   `rss_${appId}_${index}_${Date.now()}`;

      if (!title || !description) {
        console.warn(`⚠️ RSS item missing required fields: title="${title}", description="${description}"`);
        return null;
      }

      // Parse publication date
      let date = Math.floor(Date.now() / 1000);
      if (pubDate) {
        const parsedDate = new Date(pubDate);
        if (!isNaN(parsedDate.getTime())) {
          date = Math.floor(parsedDate.getTime() / 1000);
        }
      }

      // Clean up description (RSS often contains HTML/CDATA)
      let contents = description;

      // Remove CDATA wrapper if present
      contents = contents.replace(/^<!\[CDATA\[(.*?)\]\]>$/s, '$1');

      // Decode HTML entities
      contents = this.decodeHTMLEntities(contents);

      // Create SteamNewsItem compatible with existing interface
      const newsItem: SteamNewsItem = {
        gid: guid,
        title,
        url: link || `https://steamcommunity.com/app/${appId}`,
        is_external_url: !link?.includes('steamcommunity.com'),
        author,
        contents,
        feedlabel: 'Steam Community RSS',
        date,
        feedname: 'steam_community_rss',
        feed_type: 1, // Community Announcements
        appid: appId,
        tags: this.extractTagsFromRSS(item),

        // Enhanced fields will be populated during processing
        processedContent: '',
        summary: '',
        thumbnailUrl: undefined,
        detectedLanguage: undefined,
        contentLength: contents.length,
        hasImages: this.hasImages(contents),
        isEvent: false,
        eventDate: undefined
      };

      console.log(`📄 Parsed RSS item: "${title.substring(0, 50)}..." (${contents.length} chars)`);
      return newsItem;
    } catch (error) {
      console.error(`❌ Error parsing RSS item:`, error);
      return null;
    }
  }

  /**
   * Extract text content from XML element
   */
  private getElementText(parent: Element, tagName: string): string | null {
    const element = parent.querySelector(tagName);
    return element?.textContent || null;
  }

  /**
   * Decode HTML entities in text
   */
  private decodeHTMLEntities(text: string): string {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  }

  /**
   * Extract tags from RSS item (categories, etc.)
   */
  private extractTagsFromRSS(item: Element): string[] {
    const tags: string[] = [];

    // Extract categories
    const categories = item.querySelectorAll('category');
    categories.forEach(cat => {
      const text = cat.textContent?.trim();
      if (text) tags.push(text);
    });

    return tags;
  }

  /**
   * Fallback to API if RSS fails
   */
  private async fetchAPINewsAsFallback(appId: number, count: number, language: string): Promise<SteamNewsItem[]> {
    try {
      console.log(`🔄 Using API fallback for app ${appId}`);

      const languageConfig = this.getLanguageConfig(language);
      const steamLanguage = languageConfig?.steamCode || language;

      const url = new URL(this.API_BASE);
      url.searchParams.set('appid', appId.toString());
      url.searchParams.set('count', Math.min(count * 3, 20).toString());
      url.searchParams.set('maxlength', '0');
      url.searchParams.set('format', 'json');

      if (language !== 'en') {
        url.searchParams.set('l', steamLanguage);
      }

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error(`Steam API error: ${response.status}`);
      }

      const data: SteamNewsResponse = await response.json();
      const items = data.appnews?.newsitems || [];

      console.log(`🔄 API fallback returned ${items.length} items`);
      return items;
    } catch (error) {
      console.error(`❌ API fallback failed:`, error);
      return [];
    }
  }

  /**
   * Filter news items with enhanced language detection and content validation
   */
  private filterNewsItems(newsItems: SteamNewsItem[], language?: string): SteamNewsItem[] {
    console.log(`🔍 Filtering ${newsItems.length} RSS news items for language: ${language || 'any'}`);

    const filtered = newsItems.filter(item => {
      // RSS feeds are already community announcements, but double-check
      if (item.feed_type && item.feed_type !== 1) {
        return false;
      }

      // Filter out SteamDB and other unwanted sources
      const unwantedSources = ['steamdb', 'steam database', 'third party', 'steamcharts'];
      const itemText = `${item.title} ${item.author} ${item.feedname} ${item.feedlabel}`.toLowerCase();
      if (unwantedSources.some(source => itemText.includes(source))) {
        console.log(`❌ Filtering out unwanted source: "${item.title.substring(0, 50)}..."`);
        return false;
      }

      // Enhanced language filtering for RSS content
      if (language && language !== 'en') {
        const detectedLanguage = this.detectLanguage(item.contents);
        const isCorrectLanguage = detectedLanguage === language || this.isLanguageCompatible(detectedLanguage, language);

        if (!isCorrectLanguage) {
          console.log(`❌ Language mismatch: "${item.title.substring(0, 50)}..." - Expected: ${language}, Detected: ${detectedLanguage}`);
          return false;
        }

        console.log(`✅ Language match: "${item.title.substring(0, 50)}..." - Language: ${detectedLanguage}`);
      }

      // Filter out very short content (likely not useful)
      if (item.contents.length < 150) {
        console.log(`❌ Content too short: "${item.title.substring(0, 50)}..." (${item.contents.length} chars)`);
        return false;
      }

      // Filter out items without proper titles
      if (!item.title || item.title.length < 5) {
        console.log(`❌ Invalid title: "${item.title}"`);
        return false;
      }

      return true;
    });

    console.log(`📊 RSS filtering result: ${newsItems.length} → ${filtered.length} items`);

    if (filtered.length > 0) {
      // Analyze content sources
      const sources = filtered.reduce((acc, item) => {
        const source = item.feedname || item.feedlabel || 'unknown';
        acc[source] = (acc[source] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log(`📡 RSS sources:`, sources);

      if (language) {
        const languageDistribution = filtered.reduce((acc, item) => {
          const lang = this.detectLanguage(item.contents);
          acc[lang] = (acc[lang] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        console.log(`🌍 RSS language distribution:`, languageDistribution);
      }
    }

    return filtered;
  }

  /**
   * Advanced language detection using multiple strategies
   */
  private detectLanguage(content: string): string {
    if (!content || content.length < 50) {
      return 'en';
    }

    const text = content.toLowerCase();
    const words = text.split(/\s+/).filter(word => word.length > 2);

    if (words.length < 10) {
      return 'en';
    }

    // Character-based detection for non-Latin scripts
    for (const lang of this.LANGUAGES) {
      if (lang.characterPattern) {
        const matches = text.match(lang.characterPattern);
        const ratio = (matches?.length || 0) / text.length;

        if (ratio > 0.1) { // 10% threshold for character-based detection
          console.log(`🔤 Character-based detection: ${lang.code} (${(ratio * 100).toFixed(1)}%)`);
          return lang.code;
        }
      }
    }

    // Word-based detection for Latin scripts
    const languageScores: { code: string; score: number }[] = [];

    for (const lang of this.LANGUAGES) {
      if (!lang.characterPattern) { // Only for Latin-based languages
        const matches = words.filter(word => lang.commonWords.includes(word)).length;
        const score = matches / words.length;
        languageScores.push({ code: lang.code, score });
      }
    }

    // Sort by score and return the best match
    languageScores.sort((a, b) => b.score - a.score);

    if (languageScores.length > 0 && languageScores[0].score > languageScores[0].score) {
      const bestMatch = languageScores[0];
      const langConfig = this.getLanguageConfig(bestMatch.code);

      if (bestMatch.score >= (langConfig?.threshold || 0.15)) {
        console.log(`📝 Word-based detection: ${bestMatch.code} (${(bestMatch.score * 100).toFixed(1)}%)`);
        return bestMatch.code;
      }
    }

    console.log(`🔤 Defaulting to English (no clear language detected)`);
    return 'en';
  }

  /**
   * Check if detected language is compatible with expected language
   */
  private isLanguageCompatible(detected: string, expected: string): boolean {
    if (detected === expected) return true;

    // Allow English as fallback for any language if no localized content exists
    if (detected === 'en' && expected !== 'en') return true;

    return false;
  }

  /**
   * Get language configuration by code
   */
  private getLanguageConfig(code: string): LanguageConfig | undefined {
    return this.LANGUAGES.find(lang => lang.code === code || lang.steamCode === code);
  }

  /**
   * Normalize language code to standard format
   */
  private normalizeLanguage(language?: string): string {
    if (!language) return 'en';

    const normalized = language.toLowerCase();

    // Direct mapping from Steam language codes to our codes
    const steamToCode: { [key: string]: string } = {
      'english': 'en',
      'spanish': 'es',
      'german': 'de',
      'french': 'fr',
      'russian': 'ru',
      'schinese': 'zh',
      'tchinese': 'zh',
      'japanese': 'ja',
      'korean': 'ko',
      'portuguese': 'pt',
      'brazilian': 'pt',
      'italian': 'it',
      'dutch': 'nl',
      'polish': 'pl',
      'swedish': 'sv',
      'norwegian': 'no',
      'danish': 'da',
      'finnish': 'fi',
      'czech': 'cs',
      'hungarian': 'hu',
      'romanian': 'ro',
      'bulgarian': 'bg',
      'greek': 'el',
      'turkish': 'tr',
      'arabic': 'ar',
      'thai': 'th',
      'vietnamese': 'vi',
      'ukrainian': 'uk',
      'koreana': 'ko'
    };

    return steamToCode[normalized] || normalized;
  }

  /**
   * Process and enhance news items with comprehensive content optimization
   */
  private async processNewsItems(newsItems: SteamNewsItem[], appId: number): Promise<SteamNewsItem[]> {
    console.log(`🔧 Processing ${newsItems.length} news items for app ${appId}`);

    return newsItems.map((item, index) => {
      const enhanced: SteamNewsItem = {
        ...item,
        // Enhanced content processing
        processedContent: this.processContent(item.contents),
        summary: this.createSummary(item.contents),

        // Image extraction with multiple strategies
        thumbnailUrl: this.extractThumbnailUrl(item.contents, appId),
        image: this.extractImageFromContent(item.contents),

        // Language and content analysis
        detectedLanguage: this.detectLanguage(item.contents),
        contentLength: item.contents.length,
        hasImages: this.hasImages(item.contents),

        // Event detection
        isEvent: this.isEventNews(item),
        eventDate: this.extractEventDate(item.contents, item.date)
      };

      console.log(`📄 Processed "${item.title.substring(0, 50)}..." - Language: ${enhanced.detectedLanguage}, Length: ${enhanced.contentLength}, Images: ${enhanced.hasImages}`);

      return enhanced;
    });
  }

  /**
   * Check if content contains actual images (excluding YouTube placeholders)
   */
  private hasImages(content: string): boolean {
    if (!content) return false;

    // Convert clan images first to get proper URLs
    const processedContent = this.convertSteamClanImages(content);

    // Look for actual image patterns
    const imagePatterns = [
      // Steam Clan Images
      /https?:\/\/(?:steamcdn-a\.akamaihd\.net|clan\.cloudflare\.steamstatic\.com)\/steamcommunity\/public\/images\/clans\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|webp)/gi,
      // Steam Event Images
      /https?:\/\/[^\s"'<>]*(?:event_cover|event_header|800x450|1920x622)[^\s"'<>]*\.(?:jpg|jpeg|png|gif|webp)/gi,
      // Steam CDN App Images
      /https?:\/\/(?:cdn\.akamai\.steamstatic\.com|steamcdn-a\.akamaihd\.net|cdn\.cloudflare\.steamstatic\.com)\/steam\/apps\/\d+\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|webp)/gi,
      // General Steam CDN Images
      /https?:\/\/(?:cdn\.akamai\.steamstatic\.com|steamcdn-a\.akamaihd\.net|cdn\.cloudflare\.steamstatic\.com)\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|webp)/gi,
      // IMG tags
      /<img[^>]+src=["']([^"']+)["'][^>]*>/gi,
      // BB code images
      /\[img\][^\[]+\[\/img\]/gi,
      // Steam clan image placeholders
      /\{STEAM_CLAN_IMAGE\}/i
    ];

    for (const pattern of imagePatterns) {
      const matches = processedContent.match(pattern);
      if (matches) {
        // Filter out YouTube placeholder images
        const realImages = matches.filter(match =>
          !match.includes('youtube_16x9_placeholder') &&
          !match.includes('sharedFilePreviewYouTubeVideo')
        );

        if (realImages.length > 0) {
          console.log(`📸 Found ${realImages.length} real images in content`);
          return true;
        }
      }
    }

    console.log(`📸 No real images found in content`);
    return false;
  }

  /**
   * Enhanced content processing with comprehensive BB code and HTML handling
   */
  private processContent(content: string): string {
    if (!content) return '';

    console.log(`🔧 Processing content (${content.length} chars)`);

    let processed = content;

    // STEP 1: Convert Steam Clan Image URLs first
    processed = this.convertSteamClanImages(processed);

    // STEP 2: Process Steam BB codes with enhanced support
    processed = this.processBBCodes(processed);

    // STEP 3: Clean up and format HTML
    processed = this.cleanupHTML(processed);

    // STEP 4: Ensure proper spacing and formatting
    processed = this.formatSpacing(processed);

    console.log(`✅ Content processed (${processed.length} chars)`);
    return processed;
  }

  /**
   * Process Steam BB codes with comprehensive support
   */
  private processBBCodes(content: string): string {
    let processed = content;

    // Text formatting
    processed = processed
      .replace(/\[b\](.*?)\[\/b\]/gi, '<strong>$1</strong>')
      .replace(/\[i\](.*?)\[\/i\]/gi, '<em>$1</em>')
      .replace(/\[u\](.*?)\[\/u\]/gi, '<u>$1</u>')
      .replace(/\[strike\](.*?)\[\/strike\]/gi, '<del>$1</del>')
      .replace(/\[spoiler\](.*?)\[\/spoiler\]/gi, '<details><summary>Spoiler</summary>$1</details>')
      .replace(/\[code\](.*?)\[\/code\]/gi, '<pre><code>$1</code></pre>');

    // Headers with proper hierarchy
    processed = processed
      .replace(/\[h1\](.*?)\[\/h1\]/gi, '<h2>$1</h2>') // h1 -> h2 for better document structure
      .replace(/\[h2\](.*?)\[\/h2\]/gi, '<h3>$1</h3>')
      .replace(/\[h3\](.*?)\[\/h3\]/gi, '<h4>$1</h4>');

    // Lists with proper nesting
    processed = processed
      .replace(/\[list\]/gi, '<ul>')
      .replace(/\[\/list\]/gi, '</ul>')
      .replace(/\[list=1\]/gi, '<ol>')
      .replace(/\[\/list=1\]/gi, '</ol>')
      .replace(/\[\*\]/gi, '<li>');

    // Links with security attributes
    processed = processed
      .replace(/\[url=([^\]]+)\](.*?)\[\/url\]/gi, '<a href="$1" target="_blank" rel="noopener noreferrer" class="external-link">$2</a>')
      .replace(/\[url\](.*?)\[\/url\]/gi, '<a href="$1" target="_blank" rel="noopener noreferrer" class="external-link">$1</a>');

    // Images with responsive design
    processed = processed
      .replace(/\[img\](.*?)\[\/img\]/gi, '<img src="$1" alt="News image" class="news-image" style="max-width: 100%; height: auto; border-radius: 4px;" loading="lazy" />');

    // YouTube videos - replace original videos with our custom iframe in their exact position

    // 1. Process broken YouTube iframes with <br> tags and fix them (most important)
    processed = processed
      .replace(/<div[^>]*class=["']youtube-embed-container["'][^>]*>[\s\S]*?<iframe[\s\S]*?src=["']https?:\/\/(?:www\.)?youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^"']*["'][\s\S]*?<\/iframe[\s\S]*?<\/div>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // 2. Process Steam's sharedFilePreviewYouTubeVideo divs with data-youtube
    processed = processed
      .replace(/<div[^>]*data-youtube=["']([a-zA-Z0-9_-]+)["'][^>]*>.*?<\/div>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // 3. Process standalone YouTube iframes and replace them with our custom iframe
    processed = processed
      .replace(/<iframe[^>]*src=["']https?:\/\/(?:www\.)?youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^"']*["'][^>]*><\/iframe>/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // 3. Remove YouTube placeholder images (they're just placeholders) - more comprehensive
    processed = processed
      .replace(/<img[^>]*src=["'][^"']*youtube_16x9_placeholder\.gif["'][^>]*\/?>/gi, '')
      .replace(/<img[^>]*class=["'][^"']*sharedFilePreviewYouTubeVideo[^"']*["'][^>]*\/?>/gi, '')
      .replace(/<img[^>]*youtube_16x9_placeholder[^>]*>/gi, '');

    // 4. Process BB code previewyoutube tags and replace them with our custom iframe
    processed = processed
      .replace(/\[previewyoutube=([a-zA-Z0-9_-]+);[^\]]*\]/gi, (match, videoId) => {
        return `<div class="youtube-embed-container"><iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0" allowfullscreen loading="lazy" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" title="YouTube video"></iframe></div>`;
      });

    // Tables (basic support)
    processed = processed
      .replace(/\[table\]/gi, '<table class="news-table">')
      .replace(/\[\/table\]/gi, '</table>')
      .replace(/\[tr\]/gi, '<tr>')
      .replace(/\[\/tr\]/gi, '</tr>')
      .replace(/\[td\]/gi, '<td>')
      .replace(/\[\/td\]/gi, '</td>')
      .replace(/\[th\]/gi, '<th>')
      .replace(/\[\/th\]/gi, '</th>');

    // Quotes and citations
    processed = processed
      .replace(/\[quote\](.*?)\[\/quote\]/gi, '<blockquote class="news-quote">$1</blockquote>')
      .replace(/\[quote=([^\]]+)\](.*?)\[\/quote\]/gi, '<blockquote class="news-quote"><cite>$1</cite><br>$2</blockquote>');

    // Colors and sizes (convert to CSS classes for better control)
    processed = processed
      .replace(/\[color=([^\]]+)\](.*?)\[\/color\]/gi, '<span style="color: $1;">$2</span>')
      .replace(/\[size=([^\]]+)\](.*?)\[\/size\]/gi, '<span style="font-size: $1;">$2</span>');

    return processed;
  }

  /**
   * Clean up HTML and ensure proper formatting
   */
  private cleanupHTML(content: string): string {
    let processed = content;

    // Remove any remaining BB codes that weren't processed
    processed = processed.replace(/\[\/?\w+[^\]]*\]/gi, '');

    // Convert line breaks to proper HTML
    processed = processed.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>');

    // Wrap content in paragraphs if not already wrapped
    if (!processed.includes('<p>') && !processed.includes('<div>') && !processed.includes('<h')) {
      processed = `<p>${processed}</p>`;
    }

    // Fix image URLs to use proper Steam CDN
    processed = processed.replace(
      /<img([^>]+)src=["']([^"']+)["']([^>]*)>/gi,
      (match, before, src, after) => {
        let fixedSrc = src;

        // Convert relative Steam URLs to absolute
        if (src.startsWith('/steam/')) {
          fixedSrc = `https://cdn.akamai.steamstatic.com${src}`;
        } else if (src.startsWith('//')) {
          fixedSrc = `https:${src}`;
        }

        return `<img${before}src="${fixedSrc}"${after}>`;
      }
    );

    // Ensure all links open in new tab
    processed = processed.replace(
      /<a([^>]+)href=["']([^"']+)["']([^>]*)>/gi,
      (match, before, href, after) => {
        if (!after.includes('target=')) {
          after += ' target="_blank" rel="noopener noreferrer"';
        }
        return `<a${before}href="${href}"${after}>`;
      }
    );

    return processed;
  }

  /**
   * Format spacing and improve readability
   */
  private formatSpacing(content: string): string {
    let processed = content;

    // Add proper spacing around block elements
    processed = processed
      .replace(/(<\/p>)(<h[1-6])/gi, '$1\n\n$2')
      .replace(/(<\/h[1-6]>)(<p)/gi, '$1\n\n$2')
      .replace(/(<\/blockquote>)(<p)/gi, '$1\n\n$2')
      .replace(/(<\/ul>)(<p)/gi, '$1\n\n$2')
      .replace(/(<\/ol>)(<p)/gi, '$1\n\n$2');

    // Clean up excessive whitespace
    processed = processed
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();

    return processed;
  }

  /**
   * Enhanced thumbnail extraction with multiple strategies and quality scoring
   */
  private extractThumbnailUrl(content: string, appId: number): string | undefined {
    if (!content) return undefined;

    console.log(`🖼️ Extracting thumbnail for app ${appId}`);

    // Convert clan images first to get proper URLs
    const processedContent = this.convertSteamClanImages(content);

    // Collect all potential images with quality scoring
    const imageCanidates: { url: string; score: number; source: string }[] = [];

    // Strategy 1: Steam Clan Images (highest priority for community announcements)
    const clanImageRegex = /https?:\/\/(?:steamcdn-a\.akamaihd\.net|clan\.cloudflare\.steamstatic\.com)\/steamcommunity\/public\/images\/clans\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|webp)/gi;
    let match;
    while ((match = clanImageRegex.exec(processedContent)) !== null) {
      const url = this.cleanImageUrl(match[0]);
      if (url) {
        imageCanidates.push({
          url,
          score: this.scoreImageQuality(url, 'clan'),
          source: 'Steam Clan'
        });
      }
    }

    // Strategy 2: Steam Event Images (high priority for events)
    const eventImageRegex = /https?:\/\/[^\s"'<>]*(?:event_cover|event_header|800x450|1920x622)[^\s"'<>]*\.(?:jpg|jpeg|png|gif|webp)/gi;
    while ((match = eventImageRegex.exec(processedContent)) !== null) {
      const url = this.cleanImageUrl(match[0]);
      if (url) {
        imageCanidates.push({
          url,
          score: this.scoreImageQuality(url, 'event'),
          source: 'Steam Event'
        });
      }
    }

    // Strategy 3: Steam CDN App Images
    const appImageRegex = /https?:\/\/(?:cdn\.akamai\.steamstatic\.com|steamcdn-a\.akamaihd\.net|cdn\.cloudflare\.steamstatic\.com)\/steam\/apps\/\d+\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|webp)/gi;
    while ((match = appImageRegex.exec(processedContent)) !== null) {
      const url = this.cleanImageUrl(match[0]);
      if (url) {
        imageCanidates.push({
          url,
          score: this.scoreImageQuality(url, 'app'),
          source: 'Steam App'
        });
      }
    }

    // Strategy 4: General Steam CDN Images
    const steamCdnRegex = /https?:\/\/(?:cdn\.akamai\.steamstatic\.com|steamcdn-a\.akamaihd\.net|cdn\.cloudflare\.steamstatic\.com)\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|webp)/gi;
    while ((match = steamCdnRegex.exec(processedContent)) !== null) {
      const url = this.cleanImageUrl(match[0]);
      if (url) {
        imageCanidates.push({
          url,
          score: this.scoreImageQuality(url, 'cdn'),
          source: 'Steam CDN'
        });
      }
    }

    // Strategy 5: Images from IMG tags
    const imgTagRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
    while ((match = imgTagRegex.exec(processedContent)) !== null) {
      const url = this.cleanImageUrl(match[1]);
      if (url) {
        imageCanidates.push({
          url,
          score: this.scoreImageQuality(url, 'img'),
          source: 'IMG Tag'
        });
      }
    }

    // Strategy 6: Any other image URLs
    const anyImageRegex = /https?:\/\/[^\s"'<>]+\.(?:jpg|jpeg|png|gif|webp)/gi;
    while ((match = anyImageRegex.exec(processedContent)) !== null) {
      const url = this.cleanImageUrl(match[0]);
      if (url && !imageCanidates.some(c => c.url === url)) {
        imageCanidates.push({
          url,
          score: this.scoreImageQuality(url, 'other'),
          source: 'Other'
        });
      }
    }

    // Remove duplicates and filter out YouTube placeholders
    const uniqueImages = imageCanidates
      .filter((candidate, index, array) => array.findIndex(c => c.url === candidate.url) === index)
      .filter(candidate => {
        const url = candidate.url.toLowerCase();
        // Filter out YouTube placeholder images
        return !url.includes('youtube_16x9_placeholder') &&
               !url.includes('sharedfilepreviewyoutubevideo') &&
               !url.includes('youtube.com/vi/') && // Exclude YouTube thumbnails that might be in content
               candidate.score > 0; // Only include images with positive scores
      });

    uniqueImages.sort((a, b) => b.score - a.score);

    // Priority 1: If there are real images in the content, use the best one
    if (uniqueImages.length > 0) {
      const best = uniqueImages[0];
      console.log(`✅ Selected thumbnail from content images: ${best.source} (score: ${best.score}) - ${best.url.substring(0, 80)}...`);
      return best.url;
    }

    // Priority 2: If NO images but content has videos, use video thumbnail
    const videoThumbnail = this.extractVideoThumbnail(content);
    if (videoThumbnail) {
      console.log(`✅ Using YouTube video thumbnail (no images found): ${videoThumbnail}`);
      return videoThumbnail;
    }

    // Priority 3: If NO images and NO videos, fallback to game images
    const gameFallbackImage = this.getGameFallbackImage(appId, 'header'); // Default to header for thumbnails
    if (gameFallbackImage) {
      console.log(`✅ Using game fallback image (no content images or videos): ${gameFallbackImage}`);
      return gameFallbackImage;
    }

    console.log(`❌ No suitable thumbnail found`);
    return undefined;
  }

  /**
   * Clean image URL by removing trailing parameters and validating format
   */
  private cleanImageUrl(url: string): string | undefined {
    if (!url) return undefined;

    try {
      // Remove trailing junk after image extension
      let cleaned = url.replace(/\.(jpg|jpeg|png|gif|webp).*$/i, '.$1');

      // Remove query parameters and fragments that might break the image
      cleaned = cleaned.split(/[?&#]/)[0];

      // Validate it's still a proper image URL
      if (cleaned.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
        return cleaned;
      }

      return undefined;
    } catch (error) {
      console.warn('Error cleaning image URL:', error);
      return undefined;
    }
  }

  /**
   * Score image quality based on URL patterns and dimensions
   */
  private scoreImageQuality(url: string, source: string): number {
    let score = 0;
    const urlLower = url.toLowerCase();

    // Base score by source type
    switch (source) {
      case 'clan': score += 100; break;
      case 'event': score += 90; break;
      case 'app': score += 80; break;
      case 'cdn': score += 70; break;
      case 'img': score += 60; break;
      case 'other': score += 30; break;
    }

    // Bonus for specific image types
    if (urlLower.includes('event_cover') || urlLower.includes('event_header')) score += 50;
    if (urlLower.includes('capsule') || urlLower.includes('header')) score += 40;
    if (urlLower.includes('800x450') || urlLower.includes('1920x622')) score += 30;
    if (urlLower.includes('main_capsule')) score += 25;

    // Penalty for likely thumbnails or small images
    if (urlLower.includes('thumb') || urlLower.includes('small')) score -= 20;
    if (urlLower.includes('icon') || urlLower.includes('avatar')) score -= 30;

    // Heavy penalty for YouTube placeholder images
    if (urlLower.includes('youtube_16x9_placeholder')) score -= 100;

    // Bonus for Steam official domains
    if (urlLower.includes('steamstatic.com') || urlLower.includes('akamaihd.net')) score += 20;

    return Math.max(0, score);
  }

  /**
   * Extract YouTube video thumbnail from news content
   */
  private extractVideoThumbnail(content: string): string | undefined {
    if (!content) return undefined;

    // Patterns to detect YouTube videos in different formats
    const videoPatterns = [
      // YouTube embed iframes
      /src=["']https?:\/\/(?:www\.)?youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^"']*/gi,
      // Steam's data-youtube attributes
      /data-youtube=["']([a-zA-Z0-9_-]+)["']/gi,
      // BB code previewyoutube tags
      /\[previewyoutube=([a-zA-Z0-9_-]+);[^\]]*\]/gi,
      // Direct YouTube URLs
      /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/gi
    ];

    for (const pattern of videoPatterns) {
      const match = pattern.exec(content);
      if (match && match[1]) {
        const videoId = match[1];
        // Return YouTube thumbnail URL (maxresdefault for best quality, fallback to hqdefault)
        const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
        console.log(`🎥 Found YouTube video: ${videoId}, thumbnail: ${thumbnailUrl}`);
        return thumbnailUrl;
      }
    }

    return undefined;
  }

  /**
   * Get fallback images from game assets with specific type
   * @param appId - Steam app ID
   * @param type - Type of image: 'header' (banner), 'hero' (wide), 'cover' (portrait)
   */
  private getGameFallbackImage(appId: number, type: 'header' | 'hero' | 'cover' = 'header'): string | undefined {
    if (!appId || appId <= 0) return undefined;

    // Return specific image type based on context
    switch (type) {
      case 'header':
        // Banner/header image (460x215) - used for news cards
        return `https://cdn.akamai.steamstatic.com/steam/apps/${appId}/header.jpg`;

      case 'hero':
        // Library hero image (wide format) - used for modal featured images
        return `https://cdn.akamai.steamstatic.com/steam/apps/${appId}/library_hero.jpg`;

      case 'cover':
        // Portrait cover image (600x900) - used for covers/portraits
        return `https://cdn.cloudflare.steamstatic.com/steam/apps/${appId}/library_600x900.jpg`;

      default:
        return `https://cdn.akamai.steamstatic.com/steam/apps/${appId}/header.jpg`;
    }
  }

  /**
   * Extract the first image from news content (fallback)
   */
  private extractImageFromContent(content: string): string | undefined {
    return this.extractThumbnailUrl(content, 0);
  }

  /**
   * Convert Steam Clan Image URLs from {STEAM_CLAN_IMAGE} format to actual URLs
   */
  private convertSteamClanImages(content: string): string {
    // Convert {STEAM_CLAN_IMAGE}/path/to/image.ext to full Steam CDN URL
    const converted = content.replace(
      /\{STEAM_CLAN_IMAGE\}\/([^"\s\]]+)/gi,
      'https://clan.cloudflare.steamstatic.com/images/$1'
    );

    // Also handle older format
    return converted.replace(
      /\{STEAM_CLAN_LOC_IMAGE\}\/([^"\s\]]+)/gi,
      'https://clan.cloudflare.steamstatic.com/images/$1'
    );
  }

  /**
   * Create an intelligent summary from news content
   */
  private createSummary(content: string, maxLength: number = 200): string {
    if (!content) return '';

    console.log(`📝 Creating summary from content (${content.length} chars)`);

    // Step 1: Clean content by removing BB codes and HTML
    let textContent = content
      .replace(/\{STEAM_CLAN_IMAGE\}[^"\s\]]+/gi, '') // Remove clan image references
      .replace(/\[\/?\w+[^\]]*\]/gi, '') // Remove all BB codes
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/https?:\/\/[^\s]+/gi, '') // Remove URLs
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    if (!textContent) return '';

    // Step 2: Try to extract the first meaningful paragraph
    const paragraphs = textContent.split(/[.!?]+/).filter(p => p.trim().length > 20);

    if (paragraphs.length > 0) {
      let summary = paragraphs[0].trim();

      // If first paragraph is too short, add the second one
      if (summary.length < maxLength * 0.5 && paragraphs.length > 1) {
        summary += '. ' + paragraphs[1].trim();
      }

      // Ensure we don't exceed max length
      if (summary.length <= maxLength) {
        return summary + (summary.endsWith('.') || summary.endsWith('!') || summary.endsWith('?') ? '' : '.');
      }
    }

    // Step 3: Fallback to truncation with smart sentence breaking
    if (textContent.length <= maxLength) {
      return textContent;
    }

    const truncated = textContent.substring(0, maxLength);

    // Try to end at a sentence boundary
    const sentenceEnd = Math.max(
      truncated.lastIndexOf('.'),
      truncated.lastIndexOf('!'),
      truncated.lastIndexOf('?')
    );

    if (sentenceEnd > maxLength * 0.6) {
      return truncated.substring(0, sentenceEnd + 1);
    }

    // Try to end at a word boundary
    const wordEnd = truncated.lastIndexOf(' ');
    if (wordEnd > maxLength * 0.7) {
      return truncated.substring(0, wordEnd) + '...';
    }

    // Last resort: hard truncation
    return truncated + '...';
  }

  /**
   * Enhanced event detection with multilingual support
   */
  private isEventNews(item: SteamNewsItem): boolean {
    const eventKeywords = {
      en: ['event', 'tournament', 'competition', 'festival', 'celebration', 'update', 'patch', 'release', 'beta', 'alpha', 'launch', 'announcement', 'sale', 'discount', 'special', 'limited', 'season'],
      es: ['evento', 'torneo', 'competición', 'festival', 'celebración', 'actualización', 'parche', 'lanzamiento', 'beta', 'alpha', 'anuncio', 'oferta', 'descuento', 'especial', 'limitado', 'temporada'],
      de: ['ereignis', 'turnier', 'wettbewerb', 'festival', 'feier', 'aktualisierung', 'patch', 'veröffentlichung', 'beta', 'alpha', 'ankündigung', 'angebot', 'rabatt', 'spezial', 'begrenzt', 'saison'],
      fr: ['événement', 'tournoi', 'compétition', 'festival', 'célébration', 'mise à jour', 'patch', 'sortie', 'beta', 'alpha', 'annonce', 'offre', 'remise', 'spécial', 'limité', 'saison'],
      ru: ['событие', 'турнир', 'соревнование', 'фестиваль', 'празднование', 'обновление', 'патч', 'релиз', 'бета', 'альфа', 'объявление', 'предложение', 'скидка', 'специальный', 'ограниченный', 'сезон']
    };

    const title = item.title.toLowerCase();
    const feedLabel = item.feedlabel.toLowerCase();
    const content = item.contents.toLowerCase();

    // Check all language keywords
    for (const keywords of Object.values(eventKeywords)) {
      if (keywords.some(keyword =>
        title.includes(keyword) ||
        feedLabel.includes(keyword) ||
        content.includes(keyword)
      )) {
        return true;
      }
    }

    // Additional heuristics
    const eventPatterns = [
      /\d+\.\d+/g, // Version numbers (updates)
      /v\d+/gi, // Version indicators
      /coming soon/gi,
      /available now/gi,
      /new feature/gi,
      /bug fix/gi,
      /hotfix/gi
    ];

    return eventPatterns.some(pattern =>
      pattern.test(title) || pattern.test(content)
    );
  }

  /**
   * Enhanced event date extraction with multilingual support
   */
  private extractEventDate(content: string, fallbackDate: number): number {
    if (!content) return fallbackDate;

    // Multilingual date patterns
    const datePatterns = [
      // Numeric formats
      /(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})/g,
      /(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})/g,

      // English month names
      /(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2}),?\s+(\d{4})/gi,
      /(\d{1,2})\s+(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{4})/gi,

      // Spanish month names
      /(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)\s+(\d{1,2}),?\s+(\d{4})/gi,
      /(\d{1,2})\s+(de\s+)?(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)\s+(de\s+)?(\d{4})/gi,

      // German month names
      /(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)\s+(\d{1,2}),?\s+(\d{4})/gi,
      /(\d{1,2})\.\s+(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)\s+(\d{4})/gi,

      // French month names
      /(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+(\d{1,2}),?\s+(\d{4})/gi,
      /(\d{1,2})\s+(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+(\d{4})/gi,

      // Relative dates
      /in\s+(\d+)\s+(day|week|month)s?/gi,
      /within\s+(\d+)\s+(day|week|month)s?/gi,
      /coming\s+(soon|this\s+week|next\s+week|this\s+month|next\s+month)/gi
    ];

    const monthMappings = {
      // English
      january: 0, february: 1, march: 2, april: 3, may: 4, june: 5,
      july: 6, august: 7, september: 8, october: 9, november: 10, december: 11,

      // Spanish
      enero: 0, febrero: 1, marzo: 2, abril: 3, mayo: 4, junio: 5,
      julio: 6, agosto: 7, septiembre: 8, octubre: 9, noviembre: 10, diciembre: 11,

      // German
      januar: 0, februar: 1, märz: 2, juni: 5,
      juli: 6, oktober: 9, dezember: 11,

      // French
      janvier: 0, février: 1, mars: 2, avril: 3, juin: 5,
      juillet: 6, août: 7, septembre: 8, octobre: 9, novembre: 10, décembre: 11
    };

    for (const pattern of datePatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        try {
          let date: Date | null = null;

          // Handle different match patterns
          if (match[0].match(/^\d/)) {
            // Numeric date
            date = new Date(match[0]);
          } else {
            // Named month
            const monthName = match[1]?.toLowerCase() || match[2]?.toLowerCase() || match[3]?.toLowerCase();
            const day = parseInt(match[2] || match[1] || match[4]);
            const year = parseInt(match[3] || match[4] || match[5]);

            if (monthName && monthMappings[monthName] !== undefined) {
              date = new Date(year, monthMappings[monthName], day);
            }
          }

          if (date && !isNaN(date.getTime())) {
            const timestamp = Math.floor(date.getTime() / 1000);

            // Only return dates that are reasonable (not too far in past/future)
            const now = Math.floor(Date.now() / 1000);
            const oneYearAgo = now - (365 * 24 * 60 * 60);
            const oneYearFromNow = now + (365 * 24 * 60 * 60);

            if (timestamp >= oneYearAgo && timestamp <= oneYearFromNow) {
              console.log(`📅 Extracted event date: ${date.toLocaleDateString()}`);
              return timestamp;
            }
          }
        } catch (error) {
          // Continue to next pattern
          continue;
        }
      }
    }

    return fallbackDate;
  }

  /**
   * Get RSS news for multiple games with enhanced concurrent processing and language filtering
   */
  async getNewsForMultipleGames(appIds: number[], count: number = 3, language?: string): Promise<Map<number, SteamNewsItem[]>> {
    const normalizedLanguage = this.normalizeLanguage(language);
    console.log(`📚 Fetching RSS news for ${appIds.length} games in language: ${normalizedLanguage}`);

    const results = new Map<number, SteamNewsItem[]>();

    // Optimized: Check cache first for all apps to reduce network calls
    const cachedResults = new Map<number, SteamNewsItem[]>();
    const uncachedAppIds: number[] = [];

    for (const appId of appIds) {
      const cacheKey = `${appId}_${normalizedLanguage}`;
      if (this.isCacheValid(cacheKey)) {
        cachedResults.set(appId, this.cache[cacheKey].news);
        console.log(`📦 Cache hit for app ${appId}`);
      } else {
        uncachedAppIds.push(appId);
      }
    }

    console.log(`📊 Cache stats: ${cachedResults.size} cached, ${uncachedAppIds.length} need fetching`);

    // Process uncached apps in optimized batches
    const batchSize = Math.min(this.MAX_CONCURRENT_REQUESTS, 8); // Increased batch size
    const batches = [];

    for (let i = 0; i < uncachedAppIds.length; i += batchSize) {
      batches.push(uncachedAppIds.slice(i, i + batchSize));
    }

    let processedCount = 0;
    let rssSuccessCount = 0;
    let apiFallbackCount = 0;

    // Process all uncached apps in parallel with optimized concurrency
    if (uncachedAppIds.length > 0) {
      console.log(`🚀 Processing ${uncachedAppIds.length} uncached apps in ${batches.length} batches`);

      // Execute all batches in parallel for maximum speed
      const allBatchPromises = batches.map(async (batch, batchIndex) => {
        console.log(`📡 Starting batch ${batchIndex + 1}/${batches.length} (${batch.length} games)`);

        const promises = batch.map(async (appId) => {
          try {
            const news = await this.getGameNews(appId, count, normalizedLanguage);

            // Track success method
            if (news.length > 0) {
              const isRSS = news.some(item => item.feedname === 'steam_community_rss');
              if (isRSS) {
                rssSuccessCount++;
              } else {
                apiFallbackCount++;
              }
            }

            return { appId, news };
          } catch (error) {
            console.warn(`⚠️ Failed to fetch news for app ${appId}:`, error);
            return { appId, news: [] };
          }
        });

        return Promise.all(promises);
      });

      // Wait for all batches to complete
      const allBatchResults = await Promise.all(allBatchPromises);

      // Process all results
      allBatchResults.forEach((batchResults) => {
        batchResults.forEach(({ appId, news }) => {
          results.set(appId, news);
          if (news.length > 0) {
            const source = news[0]?.feedname === 'steam_community_rss' ? 'RSS' : 'API';
            console.log(`✅ App ${appId}: ${news.length} items (${source})`);
          }
        });
      });

      processedCount = uncachedAppIds.length;
    }

    // Merge cached and fetched results
    for (const [appId, news] of cachedResults) {
      results.set(appId, news);
    }

    const totalItems = Array.from(results.values()).flat().length;
    console.log(`📊 RSS fetch completed:`);
    console.log(`   • Games processed: ${results.size}`);
    console.log(`   • RSS successes: ${rssSuccessCount}`);
    console.log(`   • API fallbacks: ${apiFallbackCount}`);
    console.log(`   • Total news items: ${totalItems}`);

    return results;
  }

  /**
   * Get aggregated recent RSS news from user's library with enhanced language filtering and content optimization
   */
  async getLibraryNews(appIds: number[], maxItemsPerGame: number = 2, language?: string): Promise<SteamNewsItem[]> {
    const stopTimer = performanceTracker.startTimer('getLibraryNews');

    try {
      const normalizedLanguage = this.normalizeLanguage(language);
      console.log(`📰 Getting RSS library news for ${appIds.length} apps (max ${maxItemsPerGame} per game) in language: ${normalizedLanguage}`);

      // Limit the number of games to process for performance (use config value)
      const limitedAppIds = appIds.slice(0, NEWS_PERFORMANCE_CONFIG.MAX_GAMES_TO_PROCESS);

      const newsMap = await this.getNewsForMultipleGames(limitedAppIds, maxItemsPerGame, normalizedLanguage);

    // Flatten and collect all news with enhanced metadata
    const allNews: SteamNewsItem[] = [];
    let totalNewsCount = 0;
    let gamesWithNews = 0;
    let rssNewsCount = 0;
    let apiNewsCount = 0;

    newsMap.forEach((news, appId) => {
      if (news.length > 0) {
        gamesWithNews++;
        totalNewsCount += news.length;

        // Track RSS vs API sources
        news.forEach(item => {
          item.appid = appId; // Ensure appId is set
          allNews.push(item);

          if (item.feedname === 'steam_community_rss') {
            rssNewsCount++;
          } else {
            apiNewsCount++;
          }
        });
      }
    });

    // Sort by date (newest first) and apply intelligent filtering
    const sortedNews = allNews
      .sort((a, b) => b.date - a.date)
      .slice(0, 40); // Get more items for better RSS filtering

    // Apply quality filtering optimized for RSS content
    const qualityFiltered = this.applyRSSQualityFilter(sortedNews, normalizedLanguage);

    // Final limit
    const finalNews = qualityFiltered.slice(0, 20);

    console.log(`📊 RSS Library news summary:`);
    console.log(`   • Games processed: ${limitedAppIds.length}`);
    console.log(`   • Games with news: ${gamesWithNews}`);
    console.log(`   • RSS news items: ${rssNewsCount}`);
    console.log(`   • API fallback items: ${apiNewsCount}`);
    console.log(`   • Total news items: ${totalNewsCount}`);
    console.log(`   • After quality filter: ${qualityFiltered.length}`);
    console.log(`   • Final result: ${finalNews.length}`);

    return finalNews;
    } finally {
      stopTimer();
    }
  }

  /**
   * Apply quality filtering optimized for RSS content
   */
  private applyRSSQualityFilter(newsItems: SteamNewsItem[], language: string): SteamNewsItem[] {
    return newsItems.filter(item => {
      let score = 0;

      // Base score for RSS vs API content
      if (item.feedname === 'steam_community_rss') {
        score += 2; // Prefer RSS content
      }

      // Content length scoring (RSS often has richer content)
      if (item.contents.length > 500) score += 2;
      else if (item.contents.length > 300) score += 1;
      else if (item.contents.length < 150) return false; // Too short

      // Title quality
      if (!item.title || item.title.length < 10) return false;
      if (item.title.length > 20) score += 1;

      // Image content (RSS often has better image integration)
      const hasImage = this.hasImages(item.contents);
      if (hasImage) score += 2;

      // Language matching (more important for RSS)
      const correctLanguage = !language || language === 'en' ||
        item.detectedLanguage === language ||
        this.isLanguageCompatible(item.detectedLanguage || 'en', language);

      if (correctLanguage) {
        score += 3;
      } else if (language !== 'en') {
        score -= 2; // Penalize wrong language more for non-English
      }

      // Event detection
      if (item.isEvent) score += 1;

      // Recency bonus (RSS feeds are often more timely)
      const daysSincePublished = (Date.now() / 1000 - item.date) / (24 * 60 * 60);
      if (daysSincePublished < 7) score += 1;
      if (daysSincePublished < 1) score += 1;

      // Content richness (RSS often has better formatted content)
      if (item.contents.includes('<img') || item.contents.includes('[img]')) score += 1;
      if (item.contents.includes('<h') || item.contents.includes('[h')) score += 1;
      if (item.contents.includes('<a') || item.contents.includes('[url')) score += 1;

      console.log(`🎯 RSS Quality score for "${item.title.substring(0, 30)}...": ${score}`);

      return score >= 4; // Higher threshold for RSS quality
    });
  }

  /**
   * Legacy quality filter (kept for compatibility)
   */
  private applyQualityFilter(newsItems: SteamNewsItem[], language: string): SteamNewsItem[] {
    return this.applyRSSQualityFilter(newsItems, language);
  }

  /**
   * Enhanced cache management with language support
   */
  clearCache(appId?: number): void {
    if (appId) {
      const keysToDelete = Object.keys(this.cache).filter(key => key.startsWith(`${appId}_`));
      keysToDelete.forEach(key => delete this.cache[key]);

      if (keysToDelete.length > 0) {
        console.log(`🗑️ Cleared ${keysToDelete.length} cache entries for app ${appId}`);
      }
    } else {
      const cacheSize = Object.keys(this.cache).length;
      this.cache = {};
      console.log(`🗑️ Cleared ${cacheSize} cache entries`);
    }
  }

  /**
   * Clear cache for specific language
   */
  clearCacheForLanguage(language: string): void {
    const normalizedLanguage = this.normalizeLanguage(language);
    const keysToDelete = Object.keys(this.cache).filter(key =>
      this.cache[key].language === normalizedLanguage
    );

    keysToDelete.forEach(key => delete this.cache[key]);

    if (keysToDelete.length > 0) {
      console.log(`🗑️ Cleared ${keysToDelete.length} cache entries for language ${normalizedLanguage}`);
    }
  }

  /**
   * Enhanced cache statistics
   */
  getCacheStats(): { totalCached: number; validCached: number; totalNewsItems: number; languages: string[] } {
    const now = Date.now();
    const entries = Object.values(this.cache);
    const validEntries = entries.filter(entry => entry.expiresAt > now);
    const totalNewsItems = validEntries.reduce((sum, entry) => sum + entry.news.length, 0);
    const languages = [...new Set(validEntries.map(entry => entry.language).filter(Boolean))];

    return {
      totalCached: entries.length,
      validCached: validEntries.length,
      totalNewsItems,
      languages
    };
  }

  private isCacheValid(cacheKey: string): boolean {
    const cached = this.cache[cacheKey];
    if (!cached) return false;

    const isValid = cached.expiresAt > Date.now();
    if (!isValid) {
      console.log(`🗑️ Cache expired for key: ${cacheKey}`);
      delete this.cache[cacheKey];
    }

    return isValid;
  }

  private cacheNews(cacheKey: string, news: SteamNewsItem[], language?: string): void {
    const now = Date.now();
    this.cache[cacheKey] = {
      news,
      lastFetched: now,
      expiresAt: now + this.CACHE_DURATION,
      language: language
    };

    console.log(`💾 Cached ${news.length} news items for key: ${cacheKey}`);
  }

  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.RATE_LIMIT_DELAY) {
      const delay = this.RATE_LIMIT_DELAY - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Enhanced request queue management for better concurrency control
   */
  private async executeWithQueue<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.activeRequests >= this.MAX_CONCURRENT_REQUESTS) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0 && this.activeRequests < this.MAX_CONCURRENT_REQUESTS) {
      const operation = this.requestQueue.shift();
      if (operation) {
        this.activeRequests++;
        operation().finally(() => {
          this.activeRequests--;
          this.processQueue(); // Process next in queue
        });
      }
    }

    this.isProcessingQueue = false;
  }
}

export const steamNewsService = new SteamNewsService();
