@use "../../../scss/globals.scss";

.friends-box {
  &__section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: calc(globals.$spacing-unit * 2);
  }

  &__box {
    background-color: globals.$background-color;
    border-radius: 4px;
    border: solid 1px globals.$border-color;
    padding: calc(globals.$spacing-unit * 2);
  }

  &__list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__list-item {
    display: flex;
    cursor: pointer;
    transition: all ease 0.1s;
    color: globals.$muted-color;
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
    padding: globals.$spacing-unit;
    gap: calc(globals.$spacing-unit * 2);
    align-items: center;

    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
      text-decoration: none;
    }
  }

  &__friend-name {
    color: globals.$muted-color;
    font-weight: bold;
    font-size: globals.$body-font-size;
  }

  &__game-info {
    display: flex;
    gap: globals.$spacing-unit;
    align-items: center;
  }

  &__friend-details {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 2);
  }

  &__game-image {
    border-radius: 4px;
  }
}
