import { collectionsSublevel, levelKeys, db } from "@main/level";
import type { GameCollection } from "@types";

import { registerEvent } from "../register-event";

const updateCollection = async (
  _event: Electron.IpcMainInvokeEvent,
  collection: GameCollection
): Promise<void> => {
  try {
    const collectionsDb = collectionsSublevel(db);
    const collectionKey = levelKeys.collection(collection.id);

    // Check if collection exists
    const existingCollection = await collectionsDb.get(collectionKey).catch(() => null);
    if (!existingCollection) {
      throw new Error("Collection not found");
    }
    
    // Update with new data, preserving creation date
    const updatedCollection: GameCollection = {
      ...collection,
      createdAt: existingCollection.createdAt,
      updatedAt: new Date(),
    };
    
    await collectionsDb.put(collectionKey, updatedCollection);
  } catch (error) {
    console.error("Failed to update collection:", error);
    throw new Error("Failed to update collection");
  }
};

registerEvent("updateCollection", updateCollection);
