@use "../../../scss/globals.scss";

/**
 * LibraryQuickActions Styles
 *
 * Touch-optimized quick actions component for library games.
 * Provides essential game actions with progressive disclosure.
 *
 * Key Features:
 * - Touch-friendly button sizes (48px+ minimum)
 * - Steam Deck optimizations (52px+ targets)
 * - Multiple layout variants (card-panel, integrated, compact)
 * - Progressive disclosure for secondary actions
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 */

.library-quick-actions {
  position: relative;
  width: 100%;

  // Variant: Card Panel Layout (for expanded card actions)
  &--card-panel {
    .library-quick-actions__card-panel {
      display: flex;
      flex-direction: column;
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  // Variant: Integrated Layout (for inline card actions)
  &--integrated {
    .library-quick-actions__integrated {
      display: flex;
      align-items: center;
      gap: calc(globals.$spacing-unit * 1);
    }
  }

  // Variant: Compact Layout (for minimal space)
  &--compact {
    .library-quick-actions__integrated {
      gap: calc(globals.$spacing-unit * 0.75);
    }
  }

  // Card Panel Layout Styles
  &__secondary-row {
    display: flex;
    gap: calc(globals.$spacing-unit * 1);
    align-items: center;
    flex-wrap: nowrap; // Prevent wrapping to maintain layout consistency
  }

  // Folder Row - Top row for installed games
  &__folder-row {
    display: flex;
    gap: calc(globals.$spacing-unit * 1);
    align-items: center;
    margin-bottom: calc(globals.$spacing-unit * 0.75);
  }

  // Folder Button - Dedicated button for folder action
  &__folder-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
    min-height: 36px; // Touch target minimum
    flex: 1;
    min-width: 80px; // Prevent layout shifts

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 48px;
      padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.75);
      font-size: 15px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }

  // Primary Action Button - Touch Optimized
  &__primary-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background: linear-gradient(135deg, rgba(22, 177, 149, 0.9) 0%, rgba(22, 177, 149, 0.7) 100%);
    border: 1px solid rgba(22, 177, 149, 0.5);
    border-radius: 14px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    box-shadow:
      0 4px 16px rgba(22, 177, 149, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-height: 48px; // Touch target minimum
    flex: 1;

    &:hover {
      background: linear-gradient(135deg, rgba(22, 177, 149, 1) 0%, rgba(22, 177, 149, 0.8) 100%);
      border-color: rgba(22, 177, 149, 0.7);
      transform: translateY(-2px);
      box-shadow:
        0 6px 24px rgba(22, 177, 149, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    &:active {
      transform: translateY(0);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2.25);
      font-size: 18px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }

  &__primary-text {
    white-space: nowrap;
    font-weight: 600;
  }

  // Favorite Button - Touch Optimized
  &__favorite-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
    min-height: 36px; // Touch target minimum
    flex: 1;
    min-width: 120px; // Prevent layout shifts when text changes

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    }

    &--active {
      background: rgba(239, 68, 68, 0.15);
      border-color: rgba(239, 68, 68, 0.3);
      color: #ef4444;

      &:hover {
        background: rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.4);
        color: #dc2626;
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 48px;
      padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.75);
      font-size: 15px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }

    // In integrated layout, show as icon-only button
    .library-quick-actions--integrated & {
      width: 48px;
      height: 48px;
      padding: 0;
      justify-content: center;
      flex: none;

      .library-quick-actions__secondary-text {
        display: none;
      }

      // Steam Deck optimizations
      @media (max-width: 1280px) and (max-height: 800px) {
        width: 52px;
        height: 52px;
      }
    }
  }

  // Secondary Action Buttons - Touch Optimized
  &__secondary-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
    min-height: 36px; // Touch target minimum
    flex: 1;
    min-width: 80px; // Prevent layout shifts

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    }

    &--danger {
      &:hover {
        background: rgba(239, 68, 68, 0.15);
        border-color: rgba(239, 68, 68, 0.3);
        color: #ef4444;
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 48px;
      padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.75);
      font-size: 15px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }

  &__secondary-text {
    white-space: nowrap;
    font-weight: 500;
  }

  // Menu Container - Touch Optimized
  &__menu-container {
    position: relative;
    z-index: 1001;
  }

  // Menu Button - Touch Optimized
  &__menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 14px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.25);
      color: globals.$muted-color;
      transform: translateY(-2px);
      box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 52px;
      height: 52px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
      }
    }
  }

  // Menu Dropdown - Touch Optimized
  &__menu {
    position: absolute;
    bottom: 100%;
    right: 0;
    margin-bottom: calc(globals.$spacing-unit * 1);
    background: linear-gradient(135deg, rgba(28, 28, 28, 0.95) 0%, rgba(21, 21, 21, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 16px;
    padding: calc(globals.$spacing-unit * 1.5);
    min-width: 220px;
    max-width: 280px;
    backdrop-filter: blur(24px);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.6),
      0 6px 24px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 999999;
    animation: menuSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // Better positioning on mobile and when near screen edges
    @media (max-width: 768px) {
      min-width: 200px;
      max-width: 260px;
      padding: calc(globals.$spacing-unit * 1.25);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-width: 240px;
      max-width: 300px;
      padding: calc(globals.$spacing-unit * 1.75);
    }

    // Ensure menu doesn't go above viewport
    @media (max-height: 600px) {
      bottom: auto;
      top: 100%;
      margin-bottom: 0;
      margin-top: calc(globals.$spacing-unit * 1);
    }
  }



  // Menu Items - Touch Optimized
  &__menu-item {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.25);
    width: 100%;
    background: transparent;
    border: none;
    padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.5);
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: calc(globals.$spacing-unit * 0.75);
    min-height: 48px; // Touch target minimum

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.06) 100%);
      color: globals.$muted-color;
      transform: translateX(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    &:focus {
      outline: 2px solid rgba(22, 177, 149, 0.4);
      outline-offset: 2px;
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      color: globals.$brand-teal;
      border: 1px solid rgba(22, 177, 149, 0.3);
    }

    &--danger {
      &:hover {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(220, 53, 69, 0.1) 100%);
        color: #ff6b6b;
        border: 1px solid rgba(220, 53, 69, 0.3);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 1.75);
      font-size: 16px;
      gap: calc(globals.$spacing-unit * 1.5);
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }

    svg {
      flex-shrink: 0;
      opacity: 0.8;
      transition: opacity 0.3s ease;
    }

    &:hover svg {
      opacity: 1;
    }

    span {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 500;
    }
  }

  // Menu Divider
  &__menu-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    margin: calc(globals.$spacing-unit * 1) 0;
  }
}

// Animations
@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(12px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive adjustments for mobile devices
@media (max-width: 768px) {
  .library-quick-actions {
    &__primary-button {
      min-height: 44px;
      padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.75);
      font-size: 15px;
    }

    &__favorite-button {
      width: 44px;
      height: 44px;
    }

    &__menu-button {
      width: 44px;
      height: 44px;
    }

    &__secondary-button {
      min-height: 40px;
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1.25);
      font-size: 13px;
    }

    &__menu {
      min-width: 200px;
      max-width: 260px;
    }

    &__menu-item {
      min-height: 44px;
      padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.25);
      font-size: 14px;
    }
  }
}

// Animation delays for staggered appearance
.library-quick-actions {
  &:nth-child(1) { animation-delay: 0.05s; }
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.15s; }
  &:nth-child(4) { animation-delay: 0.2s; }
  &:nth-child(5) { animation-delay: 0.25s; }
}
