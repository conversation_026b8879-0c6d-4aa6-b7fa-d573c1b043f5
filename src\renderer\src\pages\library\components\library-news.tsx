import { useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { useSteamNews } from '@renderer/hooks/use-steam-news';
import { useLibrary } from '@renderer/hooks';
import NewsCards from './news-cards';

import './library-news.scss';

interface LibraryNewsProps {
  isVisible?: boolean;
}

export function LibraryNews({ isVisible = true }: LibraryNewsProps) {
  const { t } = useTranslation('library');
  const { library } = useLibrary();
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const containerRef = useRef<HTMLElement>(null);

  // Enhanced Intersection Observer for lazy loading with pre-loading
  useEffect(() => {
    if (!isVisible || hasBeenVisible) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setHasBeenVisible(true);
            observer.disconnect();
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '100px' // Start loading 100px before component is visible
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible, hasBeenVisible]);

  // Memoized Steam games processing - only process once
  const steamGames = useMemo(() => {
    if (!library.length) return { appIds: [], gameMap: new Map() };

    const steamGames = library.filter(game => game.shop === 'steam' && game.objectId);
    const appIds = steamGames
      .map(game => parseInt(game.objectId))
      .filter(id => !isNaN(id));

    const gameMap = new Map(
      steamGames
        .map(game => [parseInt(game.objectId), game.title])
        .filter(([id]) => !isNaN(id))
    );

    return { appIds, gameMap };
  }, [library]);

  // Extract values for easier use
  const { appIds: steamAppIds, gameMap: gameLibraryMap } = steamGames;

  // Pre-load news data when library loads (background)
  useEffect(() => {
    if (steamAppIds.length > 0 && !hasBeenVisible) {
      // Pre-warm cache in background without showing loading state
      const timer = setTimeout(() => {
        console.log('🔥 Pre-warming news cache in background');
        // This will populate cache but won't trigger loading states
        Promise.all([
          import('@renderer/services/steam-news-service'),
          import('@renderer/helpers')
        ]).then(([{ steamNewsService }, { getSteamLanguage }]) => {
          const { i18n } = require('react-i18next');
          const steamLanguage = getSteamLanguage(i18n.language);
          // Pre-load for first few games only to avoid overwhelming
          const priorityApps = steamAppIds.slice(0, 10);
          steamNewsService.getLibraryNews(priorityApps, 2, steamLanguage).catch(() => {
            // Silently fail - this is just pre-loading
          });
        });
      }, 1000); // 1 second delay to not interfere with initial page load

      return () => clearTimeout(timer);
    }
  }, [steamAppIds, hasBeenVisible]);

  // Only fetch news when component has been visible and we have Steam games
  const shouldFetchNews = hasBeenVisible && isVisible && steamAppIds.length > 0;

  const { news, isLoading, error } = useSteamNews(steamAppIds, {
    enabled: shouldFetchNews,
    maxItemsPerGame: 3,
    refreshInterval: 30 * 60 * 1000 // 30 minutes for better performance
  });

  // Enhance news items with game titles using Map for O(1) lookup
  const enhancedNews = useMemo(() => {
    if (!news.length) return [];

    return news.map(item => ({
      ...item,
      gameTitle: gameLibraryMap.get(item.appid) || item.feedname
    }));
  }, [news, gameLibraryMap]);

  // Early returns for better performance
  if (!isVisible || steamAppIds.length === 0) {
    return null;
  }

  if (error) {
    return (
      <section ref={containerRef} className="library-news">
        <div className="library-news__error">
          <p>{t('news_error')}: {error}</p>
        </div>
      </section>
    );
  }

  // Show skeleton cards until component becomes visible
  if (!hasBeenVisible) {
    return (
      <section ref={containerRef} className="library-news">
        <div className="news-cards-container">
          <div className="news-cards-header">
            <h2>{t('whats_new')}</h2>
          </div>
          <div className="news-cards-loading">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="news-card-skeleton" />
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={containerRef} className="library-news">
      <NewsCards
        newsItems={enhancedNews}
        isLoading={isLoading}
      />
    </section>
  );
}
