@use "../../../scss/globals.scss";

.library-filter-chips {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: calc(globals.$spacing-unit * 2);
  margin: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 4);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @media (max-width: 768px) {
    margin: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 3);
    padding: calc(globals.$spacing-unit * 1.5);
  }

  &__content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__header {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    flex-shrink: 0;
  }

  &__label {
    font-size: globals.$small-font-size;
    font-weight: 600;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__count {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 20px;
    background: linear-gradient(135deg, rgba(62, 98, 192, 0.9) 0%, rgba(62, 98, 192, 0.7) 100%);
    border: 1px solid rgba(62, 98, 192, 0.5);
    border-radius: 10px;
    color: white;
    font-size: 11px;
    font-weight: 700;
    line-height: 1;
    padding: 0 calc(globals.$spacing-unit * 0.5);
    box-shadow: 0 2px 8px rgba(62, 98, 192, 0.3);
  }

  &__chips {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;

    @media (max-width: 768px) {
      justify-content: flex-start;
    }
  }

  &__chip {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.75);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    border-radius: 12px;
    font-size: globals.$small-font-size;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: chipSlideIn 0.3s ease-out;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);

      &::before {
        opacity: 1;
      }
    }

    &--search {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 193, 7, 0.1) 100%);
      border: 1px solid rgba(255, 193, 7, 0.3);
      color: rgba(255, 193, 7, 0.9);

      &:hover {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.15) 100%);
        border-color: rgba(255, 193, 7, 0.4);
      }
    }

    &--sort {
      background: linear-gradient(135deg, rgba(138, 43, 226, 0.15) 0%, rgba(138, 43, 226, 0.1) 100%);
      border: 1px solid rgba(138, 43, 226, 0.3);
      color: rgba(138, 43, 226, 0.9);

      &:hover {
        background: linear-gradient(135deg, rgba(138, 43, 226, 0.2) 0%, rgba(138, 43, 226, 0.15) 100%);
        border-color: rgba(138, 43, 226, 0.4);
      }
    }

    &--status {
      background: linear-gradient(135deg, rgba(28, 151, 73, 0.15) 0%, rgba(28, 151, 73, 0.1) 100%);
      border: 1px solid rgba(28, 151, 73, 0.3);
      color: rgba(28, 151, 73, 0.9);

      &:hover {
        background: linear-gradient(135deg, rgba(28, 151, 73, 0.2) 0%, rgba(28, 151, 73, 0.15) 100%);
        border-color: rgba(28, 151, 73, 0.4);
      }
    }

    &--genre {
      background: linear-gradient(135deg, rgba(62, 98, 192, 0.15) 0%, rgba(62, 98, 192, 0.1) 100%);
      border: 1px solid rgba(62, 98, 192, 0.3);
      color: rgba(62, 98, 192, 0.9);

      &:hover {
        background: linear-gradient(135deg, rgba(62, 98, 192, 0.2) 0%, rgba(62, 98, 192, 0.15) 100%);
        border-color: rgba(62, 98, 192, 0.4);
      }
    }

    svg {
      flex-shrink: 0;
      opacity: 0.8;
      transition: opacity 0.2s ease;
    }

    &:hover svg {
      opacity: 1;
    }
  }

  &__chip-text {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.2;
  }

  &__chip-remove {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: rgba(0, 0, 0, 0.2);
    border: none;
    border-radius: 50%;
    color: currentColor;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    opacity: 0.7;

    &:hover {
      background: rgba(0, 0, 0, 0.4);
      opacity: 1;
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.9);
    }
  }

  &__clear-all {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.75);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1.25);
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.15) 0%, rgba(220, 53, 69, 0.1) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 12px;
    color: rgba(220, 53, 69, 0.9);
    font-size: globals.$small-font-size;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;

    &:hover {
      background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(220, 53, 69, 0.15) 100%);
      border-color: rgba(220, 53, 69, 0.4);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    svg {
      opacity: 0.8;
      transition: opacity 0.2s ease;
    }

    &:hover svg {
      opacity: 1;
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes chipSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// Stagger animation for chips
.library-filter-chips__chip {
  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.05}s;
    }
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .library-filter-chips {
    margin: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 1);

    &__content {
      gap: calc(globals.$spacing-unit * 1);
    }

    &__chips {
      gap: calc(globals.$spacing-unit * 0.75);
    }

    &__chip {
      padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
      font-size: 11px;
    }

    &__clear-all {
      padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 1);
      font-size: 11px;
    }
  }
}
