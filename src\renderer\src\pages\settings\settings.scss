@use "../../scss/globals.scss";

.settings {
  &__container {
    padding: 24px;
    width: 100%;
    display: flex;
  }

  &__content {
    background-color: globals.$background-color;
    width: 100%;
    height: 100%;
    padding: calc(globals.$spacing-unit * 3);
    border: solid 1px globals.$border-color;
    box-shadow: 0px 0px 15px 0px #000000;
    border-radius: 8px;
    gap: calc(globals.$spacing-unit * 2);
    display: flex;
    flex-direction: column;
  }

  &__categories {
    display: flex;
    gap: globals.$spacing-unit;
  }
}
