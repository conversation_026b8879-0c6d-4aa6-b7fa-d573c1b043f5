import { collectionsSublevel, levelKeys, db } from "@main/level";

import { registerEvent } from "../register-event";

const removeGameFromCollection = async (
  _event: Electron.IpcMainInvokeEvent,
  collectionId: string,
  gameId: string
): Promise<void> => {
  try {
    const collectionsDb = collectionsSublevel(db);
    const collectionKey = levelKeys.collection(collectionId);

    // Get existing collection
    const collection = await collectionsDb.get(collectionKey);

    // Ensure gameIds is an array
    const gameIds = collection.gameIds || [];

    // Remove game from collection
    const updatedCollection = {
      ...collection,
      gameIds: gameIds.filter(id => id !== gameId),
      updatedAt: new Date(),
    };
    
    await collectionsDb.put(collectionKey, updatedCollection);
  } catch (error) {
    console.error("Failed to remove game from collection:", error);
    throw new Error("Failed to remove game from collection");
  }
};

registerEvent("removeGameFromCollection", removeGameFromCollection);
