import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, CalendarIcon, LinkExternalIcon } from '@primer/octicons-react';
import { SteamNewsItem } from '@renderer/services/steam-news-service';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale/es';
import { enUS } from 'date-fns/locale/en-US';
import { useTranslation } from 'react-i18next';
import NewsModal from './news-modal';

import './news-cards.scss';

interface NewsCardsProps {
  newsItems: SteamNewsItem[];
  isLoading?: boolean;
}

const NewsCards: React.FC<NewsCardsProps> = ({ newsItems, isLoading }) => {
  const { t, i18n } = useTranslation("library");
  const [selectedNews, setSelectedNews] = useState<SteamNewsItem | null>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Memoize locale to prevent unnecessary re-renders
  const locale = useMemo(() => i18n.language === 'es' ? es : enUS, [i18n.language]);

  // Memoize scroll button check function
  const checkScrollButtons = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    setCanScrollLeft(container.scrollLeft > 0);
    setCanScrollRight(
      container.scrollLeft < container.scrollWidth - container.clientWidth
    );
  }, []);

  useEffect(() => {
    checkScrollButtons();
  }, [newsItems, checkScrollButtons]);

  // Memoize scroll function
  const scroll = useCallback((direction: 'left' | 'right') => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const scrollAmount = 320; // Width of one card plus gap
    const newScrollLeft = direction === 'left'
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount;

    container.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    });
  }, []);

  // Memoize date formatting function
  const formatDate = useCallback((timestamp: number) => {
    try {
      const date = new Date(timestamp * 1000);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return t('unknown_date');
    }
  }, [locale, t]);

  const formatEventDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString(i18n.language, {
      month: 'short',
      day: 'numeric'
    });
  };

  // Memoize image functions for better performance
  const getDefaultImage = useCallback((item: SteamNewsItem) => {
    // Use game header (banner) image for news cards - horizontal format fits cards better
    return `https://cdn.akamai.steamstatic.com/steam/apps/${item.appid}/header.jpg`;
  }, []);

  const getNewsImage = useCallback((item: SteamNewsItem) => {
    // Priority: thumbnailUrl (includes video thumbnails) > image > game header (banner)
    return item.thumbnailUrl || item.image || getDefaultImage(item);
  }, [getDefaultImage]);

  if (isLoading) {
    return (
      <div className="news-cards-container">
        <div className="news-cards-header">
          <h2>{t('whats_new')}</h2>
        </div>
        <div className="news-cards-loading">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="news-card-skeleton" />
          ))}
        </div>
      </div>
    );
  }

  if (!newsItems.length) {
    return (
      <div className="news-cards-container">
        <div className="news-cards-header">
          <h2>{t('whats_new')}</h2>
        </div>
        <div className="news-cards-empty">
          <p>{t('no_news_available')}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="news-cards-container">
        <div className="news-cards-header">
          <h2>{t('whats_new')}</h2>
          <div className="news-cards-controls">
            <button
              className={`news-scroll-button ${!canScrollLeft ? 'disabled' : ''}`}
              onClick={() => scroll('left')}
              disabled={!canScrollLeft}
            >
              <ChevronLeftIcon size={16} />
            </button>
            <button
              className={`news-scroll-button ${!canScrollRight ? 'disabled' : ''}`}
              onClick={() => scroll('right')}
              disabled={!canScrollRight}
            >
              <ChevronRightIcon size={16} />
            </button>
          </div>
        </div>

        <div 
          className="news-cards-scroll"
          ref={scrollContainerRef}
          onScroll={checkScrollButtons}
        >
          <div className="news-cards-list">
            {newsItems.map((item) => (
              <div
                key={item.gid}
                className={`news-card ${item.isEvent ? 'event-card' : ''}`}
                onClick={() => setSelectedNews(item)}
              >
                <div className="news-card-image">
                  <img
                    src={getNewsImage(item)}
                    alt={item.title}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      // Fallback order for cards: header -> hero -> cover
                      const fallbackImages = [
                        `https://cdn.akamai.steamstatic.com/steam/apps/${item.appid}/header.jpg`,
                        `https://cdn.akamai.steamstatic.com/steam/apps/${item.appid}/library_hero.jpg`,
                        `https://cdn.cloudflare.steamstatic.com/steam/apps/${item.appid}/library_600x900.jpg`
                      ];

                      // Try next fallback image
                      const currentSrc = target.src;
                      const currentIndex = fallbackImages.findIndex(img => currentSrc.includes(img.split('/').pop() || ''));
                      const nextIndex = currentIndex + 1;

                      if (nextIndex < fallbackImages.length) {
                        target.src = fallbackImages[nextIndex];
                      }
                    }}
                  />
                  {item.isEvent && (
                    <div className="event-badge">
                      <CalendarIcon size={12} />
                      <span>{formatEventDate(item.eventDate || item.date)}</span>
                    </div>
                  )}
                  {item.is_external_url && (
                    <div className="external-badge">
                      <LinkExternalIcon size={12} />
                    </div>
                  )}
                </div>

                <div className="news-card-content">
                  <div className="news-card-meta">
                    <span className="news-card-game">{item.gameTitle || item.feedname}</span>
                    <span className="news-card-date">{formatDate(item.date)}</span>
                  </div>
                  
                  <h3 className="news-card-title">{item.title}</h3>
                  
                  {item.summary && (
                    <p className="news-card-summary">{item.summary}</p>
                  )}

                  <div className="news-card-footer">
                    <span className="news-card-label">{item.feedlabel}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {selectedNews && (
        <NewsModal
          newsItem={selectedNews}
          onClose={() => setSelectedNews(null)}
        />
      )}
    </>
  );
};

export default NewsCards;
