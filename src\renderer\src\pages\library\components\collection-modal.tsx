import { useState, useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { createPortal } from "react-dom";
import { XIcon, CheckIcon, AlertIcon } from "@primer/octicons-react";

import { <PERSON><PERSON>, TextField } from "@renderer/components";
import { useLibraryCollections, useToast } from "@renderer/hooks";

import type { GameCollection } from "@types";

import "./collection-modal.scss";

interface CollectionModalProps {
  collection?: GameCollection;
  onClose: () => void;
  onSave?: () => void;
}

const COLLECTION_COLORS = [
  "#6b7280", // Cool Gray
  "#8b5cf6", // Soft Purple
  "#10b981", // Emerald
  "#f59e0b", // Amber
  "#ef4444", // Soft Red
  "#84cc16", // Lime
  "#f97316", // Orange
  "#ec4899", // Pink
  "#06b6d4", // <PERSON><PERSON> (muted)
  "#8b5a2b", // <PERSON>
  "#64748b", // Slate
  "#71717a", // Zinc
];

export function CollectionModal({ collection, onClose, onSave }: CollectionModalProps) {
  const { t } = useTranslation("library");
  const { createCollection, editCollection, collections } = useLibraryCollections();
  const { showSuccessToast, showErrorToast } = useToast();

  const [name, setName] = useState(collection?.name ? String(collection.name) : "");
  const [description, setDescription] = useState(collection?.description ? String(collection.description) : "");
  const [selectedColor, setSelectedColor] = useState(
    collection?.color || COLLECTION_COLORS[0]
  );
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    name?: string;
    general?: string;
  }>({});

  const isEditing = Boolean(collection);
  const safeName = String(name || "");
  const isFormValid = safeName.trim().length > 0 && safeName.trim().length <= 50;
  const nameExists = collections.some(c =>
    c.name.toLowerCase() === safeName.trim().toLowerCase() &&
    c.id !== collection?.id
  );



  // Validation
  useEffect(() => {
    const newErrors: typeof errors = {};

    if (safeName.trim().length === 0) {
      newErrors.name = t("collection_name_required");
    } else if (safeName.trim().length > 50) {
      newErrors.name = t("collection_name_too_long");
    } else if (nameExists) {
      newErrors.name = t("collection_name_exists");
    }

    setErrors(newErrors);
  }, [safeName, nameExists, t]);

  const handleSave = useCallback(async () => {
    if (!isFormValid || nameExists) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const collectionData = {
        name: safeName.trim(),
        description: String(description || "").trim() || undefined,
        color: selectedColor,
      };

      if (isEditing && collection) {
        await editCollection(collection.id, collectionData);
        showSuccessToast(t("collection_updated"));
      } else {
        await createCollection(collectionData);
        showSuccessToast(t("collection_created"));
      }

      onSave?.();
      onClose();
    } catch (error) {
      console.error("Failed to save collection:", error);
      setErrors({
        general: t("collection_save_failed"),
      });
      showErrorToast(t("collection_save_failed"));
    } finally {
      setIsLoading(false);
    }
  }, [name, description, selectedColor, collection, isEditing, isFormValid, nameExists, createCollection, editCollection, onSave, onClose, t, showSuccessToast, showErrorToast]);

  const handleColorSelect = useCallback((color: string) => {
    setSelectedColor(color);
  }, []);

  return createPortal(
    <div className="collection-modal-backdrop" onClick={onClose}>
      <div className="collection-modal" onClick={(e) => e.stopPropagation()}>
        <div className="collection-modal__header">
          <h2 className="collection-modal__title">
            {isEditing ? t("edit_collection") : t("create_collection")}
          </h2>
          <button
            type="button"
            className="collection-modal__close"
            onClick={onClose}
          >
            <XIcon size={20} />
          </button>
        </div>

        <div className="collection-modal__content">
          <div className="collection-modal__field">
            <label className="collection-modal__label">
              {t("collection_name")} *
            </label>
            <TextField
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder={t("enter_collection_name")}
              theme="dark"
              maxLength={50}
              error={errors.name}
            />
            {errors.name && (
              <div className="collection-modal__error">
                <AlertIcon size={14} />
                {errors.name}
              </div>
            )}
            <div className="collection-modal__field-hint">
              {safeName.length}/50 {t("characters")}
            </div>
          </div>

          <div className="collection-modal__field">
            <label className="collection-modal__label">
              {t("description")} ({t("optional")})
            </label>
            <textarea
              className="collection-modal__textarea"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={t("enter_collection_description")}
              maxLength={200}
              rows={3}
            />
            <div className="collection-modal__field-hint">
              {String(description || "").length}/200 {t("characters")}
            </div>
          </div>

          <div className="collection-modal__field">
            <label className="collection-modal__label">
              {t("collection_color")}
            </label>
            <div className="collection-modal__color-picker">
              {COLLECTION_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`collection-modal__color-option ${
                    selectedColor === color
                      ? "collection-modal__color-option--selected"
                      : ""
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => handleColorSelect(color)}
                  aria-label={`Select color ${color}`}
                >
                  {selectedColor === color && (
                    <CheckIcon size={16} color="white" />
                  )}
                </button>
              ))}
            </div>
          </div>

          <div className="collection-modal__preview">
            <div className="collection-modal__preview-label">
              {t("preview")}:
            </div>
            <div className="collection-modal__preview-item">
              <div 
                className="collection-modal__preview-color"
                style={{ backgroundColor: selectedColor }}
              />
              <span className="collection-modal__preview-name">
                {safeName || t("collection_name")}
              </span>
            </div>
          </div>
        </div>

        <div className="collection-modal__footer">
          {errors.general && (
            <div className="collection-modal__error collection-modal__error--general">
              <AlertIcon size={16} />
              {errors.general}
            </div>
          )}
          <div className="collection-modal__actions">
            <Button onClick={onClose} theme="outline" disabled={isLoading}>
              {t("cancel")}
            </Button>
            <Button
              onClick={handleSave}
              theme="primary"
              disabled={!isFormValid || nameExists || isLoading}
              loading={isLoading}
            >
              {isLoading
                ? t("saving")
                : isEditing
                  ? t("save_changes")
                  : t("create_collection")
              }
            </Button>
          </div>

        </div>
      </div>
    </div>,
    document.body
  );
}
