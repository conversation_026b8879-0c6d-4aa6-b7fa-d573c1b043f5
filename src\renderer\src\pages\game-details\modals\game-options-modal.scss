@use "../../../scss/globals.scss";

.game-options-modal {
  &__container {
    display: flex;
    gap: calc(globals.$spacing-unit * 2);
    flex-direction: column;
  }

  &__section {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
  }

  &__header {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;

    &-description {
      font-weight: 400;
    }
  }

  &__cloud-sync-label {
    display: flex;
    gap: globals.$spacing-unit;
    align-items: center;
  }

  &__cloud-sync-hydra-cloud {
    background: linear-gradient(270deg, #16b195 50%, #3e62c0 100%);
    color: #fff;
    padding: 0 globals.$spacing-unit;
    border-radius: 4px;
    font-size: globals.$small-font-size;
  }

  &__row {
    display: flex;
    gap: globals.$spacing-unit;
  }

  &__executable-field {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;

    &-input {
      width: 100%;
    }

    &-buttons {
      display: flex;
      gap: globals.$spacing-unit;
    }
  }

  &__wine-prefix {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);

    &-input {
      width: 100%;
    }
  }

  &__launch-options {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;

    &-input {
      width: 100%;
    }
  }

  &__downloads {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
  }

  &__danger-zone {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;

    &-description {
      font-weight: 400;
    }

    &-buttons {
      display: flex;
      gap: globals.$spacing-unit;
    }
  }
}
