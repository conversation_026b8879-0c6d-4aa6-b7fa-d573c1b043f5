import { useCallback, useState } from "react";
import { useAppDispatch, useAppSelector } from "./redux";
import { setLibrary } from "@renderer/features";

export function useLibrary() {
  const dispatch = useAppDispatch();
  const library = useAppSelector((state) => state.library.value);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateLibrary = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const updatedLibrary = await window.electron.getLibrary();
      dispatch(setLibrary(updatedLibrary));
      return updatedLibrary;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load library";
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [dispatch]);

  return { library, updateLibrary, isLoading, error };
}
