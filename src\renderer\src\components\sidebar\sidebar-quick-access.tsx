import { useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import {
  ClockIcon,
  DownloadIcon,
  StarIcon,
  PlayIcon,
  StopIcon,
  CheckIcon,
} from "@primer/octicons-react";

import type { LibraryGame } from "@types";
import { useLibrary, useDownload } from "@renderer/hooks";
import { buildGameDetailsPath } from "@renderer/helpers";
import { wasPlayedRecently } from "@renderer/utils/date-utils";

import "./sidebar-quick-access.scss";

interface QuickAccessGameProps {
  game: LibraryGame;
  onClick: (game: LibraryGame) => void;
  status?: "downloading" | "paused" | "installed" | "favorite";
  progress?: number;
}

function QuickAccessGame({ game, onClick, status, progress }: QuickAccessGameProps) {
  const { t } = useTranslation("library");
  const location = useLocation();
  
  const isActive = location.pathname === buildGameDetailsPath({
    ...game,
    objectId: game.objectId,
  });

  const getStatusIcon = () => {
    switch (status) {
      case "downloading":
        return <DownloadIcon size={12} />;
      case "paused":
        return <StopIcon size={12} />;
      case "installed":
        return <CheckIcon size={12} />;
      case "favorite":
        return <StarIcon size={12} />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "downloading":
        return "#3e62c0";
      case "paused":
        return "#f59e0b";
      case "installed":
        return "#1c9749";
      case "favorite":
        return "#ffc107";
      default:
        return "rgba(255, 255, 255, 0.6)";
    }
  };

  return (
    <button
      type="button"
      className={`sidebar-quick-access__game ${
        isActive ? "sidebar-quick-access__game--active" : ""
      }`}
      onClick={() => onClick(game)}
      title={game.title}
    >
      <div className="sidebar-quick-access__game-icon">
        {game.iconUrl ? (
          <img
            src={game.iconUrl}
            alt={game.title}
            loading="lazy"
          />
        ) : (
          <div className="sidebar-quick-access__game-icon-placeholder">
            <PlayIcon size={16} />
          </div>
        )}
        
        {status && (
          <div 
            className="sidebar-quick-access__game-status"
            style={{ backgroundColor: getStatusColor() }}
          >
            {getStatusIcon()}
          </div>
        )}
        
        {status === "downloading" && progress !== undefined && (
          <div className="sidebar-quick-access__game-progress">
            <div 
              className="sidebar-quick-access__game-progress-bar"
              style={{ width: `${progress}%` }}
            />
          </div>
        )}
      </div>
      
      <div className="sidebar-quick-access__game-info">
        <span className="sidebar-quick-access__game-title">
          {game.title}
        </span>
        {status === "downloading" && progress !== undefined && (
          <span className="sidebar-quick-access__game-subtitle">
            {progress}% {t("downloading")}
          </span>
        )}
      </div>
    </button>
  );
}

export function SidebarQuickAccess() {
  const { t } = useTranslation("library");
  const { library } = useLibrary();
  const { lastPacket, progress } = useDownload();
  const navigate = useNavigate();
  const location = useLocation();

  const handleGameClick = useCallback((game: LibraryGame) => {
    const path = buildGameDetailsPath({
      ...game,
      objectId: game.objectId,
    });
    if (path !== location.pathname) {
      navigate(path);
    }
  }, [navigate, location.pathname]);

  // Get recently played games (last 5)
  const recentlyPlayedGames = useMemo(() => {
    return library
      .filter(game => wasPlayedRecently(game.lastTimePlayed))
      .sort((a, b) => {
        const aTime = a.lastTimePlayed ? new Date(a.lastTimePlayed).getTime() : 0;
        const bTime = b.lastTimePlayed ? new Date(b.lastTimePlayed).getTime() : 0;
        return bTime - aTime;
      })
      .slice(0, 5);
  }, [library]);

  // Get currently downloading games
  const downloadingGames = useMemo(() => {
    return library.filter(game => 
      game.download?.status === "downloading" || 
      game.download?.queued ||
      lastPacket?.gameId === game.id
    ).slice(0, 3);
  }, [library, lastPacket]);

  // Get top favorite games (most played favorites)
  const topFavoriteGames = useMemo(() => {
    return library
      .filter(game => game.favorite)
      .sort((a, b) => (b.playTimeInMilliseconds || 0) - (a.playTimeInMilliseconds || 0))
      .slice(0, 4);
  }, [library]);

  const getCurrentProgress = (game: LibraryGame) => {
    if (lastPacket?.gameId === game.id) {
      return progress;
    }
    return undefined;
  };

  const getGameStatus = (game: LibraryGame): "downloading" | "paused" | "installed" | "favorite" | undefined => {
    if (lastPacket?.gameId === game.id || game.download?.status === "downloading") {
      return "downloading";
    }
    if (game.download?.status === "paused") {
      return "paused";
    }
    if (game.executablePath) {
      return "installed";
    }
    if (game.favorite) {
      return "favorite";
    }
    return undefined;
  };

  if (recentlyPlayedGames.length === 0 && downloadingGames.length === 0 && topFavoriteGames.length === 0) {
    return null;
  }

  return (
    <section className="sidebar-quick-access">
      <div className="sidebar-quick-access__header">
        <ClockIcon size={16} />
        <h3 className="sidebar-quick-access__title">{t("quick_access")}</h3>
      </div>

      <div className="sidebar-quick-access__content">
        {/* Currently Downloading */}
        {downloadingGames.length > 0 && (
          <div className="sidebar-quick-access__section">
            <h4 className="sidebar-quick-access__section-title">
              <DownloadIcon size={14} />
              {t("downloading")}
            </h4>
            <div className="sidebar-quick-access__games">
              {downloadingGames.map((game) => (
                <QuickAccessGame
                  key={`downloading-${game.id}`}
                  game={game}
                  onClick={handleGameClick}
                  status={game.download?.status === "paused" ? "paused" : "downloading"}
                  progress={getCurrentProgress(game)}
                />
              ))}
            </div>
          </div>
        )}

        {/* Recently Played */}
        {recentlyPlayedGames.length > 0 && (
          <div className="sidebar-quick-access__section">
            <h4 className="sidebar-quick-access__section-title">
              <ClockIcon size={14} />
              {t("recently_played")}
            </h4>
            <div className="sidebar-quick-access__games">
              {recentlyPlayedGames.map((game) => (
                <QuickAccessGame
                  key={`recent-${game.id}`}
                  game={game}
                  onClick={handleGameClick}
                  status={getGameStatus(game)}
                />
              ))}
            </div>
          </div>
        )}

        {/* Top Favorites */}
        {topFavoriteGames.length > 0 && (
          <div className="sidebar-quick-access__section">
            <h4 className="sidebar-quick-access__section-title">
              <StarIcon size={14} />
              {t("top_favorites")}
            </h4>
            <div className="sidebar-quick-access__games">
              {topFavoriteGames.map((game) => (
                <QuickAccessGame
                  key={`favorite-${game.id}`}
                  game={game}
                  onClick={handleGameClick}
                  status="favorite"
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
