import { useState, useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { createPortal } from "react-dom";
import {
  BookIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FileDirectoryIcon,
  KebabHorizontalIcon,
  AppsIcon
} from "@primer/octicons-react";

import { useAppDispatch, useAppSelector, useLibrary } from "@renderer/hooks";
import { setSelectedCollection } from "@renderer/features/library-collections-slice";
import { CollectionModal } from "./collection-modal";
import { DeleteCollectionModal } from "./delete-collection-modal";

import type { GameCollection } from "@types";

import "./collections-tabs.scss";

interface CollectionsTabsProps {
  onCreateCollection: () => void;
}

export function CollectionsTabs({ onCreateCollection }: CollectionsTabsProps) {
  const { t } = useTranslation("library");
  const dispatch = useAppDispatch();
  const { library } = useLibrary();
  
  const { collections, selectedCollection } = useAppSelector(
    (state) => state.libraryCollections
  );

  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingCollection, setEditingCollection] = useState<GameCollection | null>(null);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const actionButtonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  const handleCollectionSelect = useCallback(
    (collectionId: string | null) => {
      dispatch(setSelectedCollection(collectionId));
      setActiveDropdown(null);
    },
    [dispatch]
  );

  const getCollectionGameCount = useCallback(
    (collectionId: string) => {
      const collection = collections.find((c) => c.id === collectionId);
      if (!collection) return 0;
      
      return collection.gameIds.filter((gameId) =>
        library.some((game) => game.id === gameId)
      ).length;
    },
    [collections, library]
  );

  const handleEditCollection = useCallback((collection: GameCollection) => {
    setEditingCollection(collection);
    setShowEditModal(true);
    setActiveDropdown(null);
  }, []);

  const handleDeleteCollection = useCallback((collection: GameCollection) => {
    setEditingCollection(collection);
    setShowDeleteModal(true);
    setActiveDropdown(null);
  }, []);

  const handleDropdownToggle = useCallback((collectionId: string) => {
    if (activeDropdown === collectionId) {
      setActiveDropdown(null);
    } else {
      const buttonElement = actionButtonRefs.current[collectionId];
      if (buttonElement) {
        const rect = buttonElement.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + 8,
          left: rect.right - 140, // Align right edge of dropdown with right edge of button
        });
      }
      setActiveDropdown(collectionId);
    }
  }, [activeDropdown]);

  const handleCloseModals = useCallback(() => {
    setShowEditModal(false);
    setShowDeleteModal(false);
    setEditingCollection(null);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    if (activeDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [activeDropdown]);

  return (
    <>
      <div className="collections-tabs">
        <div className="collections-tabs__header">
          <h3>{t("collections")}</h3>
          <button
            type="button"
            className="collections-tabs__create-button"
            onClick={onCreateCollection}
            aria-label={t("new_collection")}
          >
            <PlusIcon size={16} />
            {t("new_collection")}
          </button>
        </div>

        <div className="collections-tabs__container">
          {/* All Games Tab */}
          <div
            className={`collections-tabs__tab-wrapper ${
              selectedCollection === null ? "collections-tabs__tab-wrapper--active" : ""
            }`}
          >
            <button
              type="button"
              className="collections-tabs__tab"
              onClick={() => handleCollectionSelect(null)}
            >
              <div className="collections-tabs__tab-icon">
                <div className="collections-tabs__tab-color collections-tabs__tab-color--all-games">
                  <AppsIcon size={14} />
                </div>
              </div>
              <span className="collections-tabs__tab-name">{t("all_games")}</span>
              <span className="collections-tabs__tab-count">{library.length}</span>
            </button>
          </div>

          {/* Collection Tabs */}
          {collections.map((collection) => (
            <div
              key={collection.id}
              className={`collections-tabs__tab-wrapper ${
                selectedCollection === collection.id ? "collections-tabs__tab-wrapper--active" : ""
              }`}
              style={selectedCollection === collection.id ? {
                borderBottom: `3px solid ${collection.color}`,
                boxShadow: `0 2px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px ${collection.color}20`
              } : {}}
            >
              <button
                type="button"
                className="collections-tabs__tab"
                onClick={() => handleCollectionSelect(collection.id)}
              >
                <div className="collections-tabs__tab-icon">
                  <div
                    className="collections-tabs__tab-color"
                    style={{ backgroundColor: collection.color }}
                  >
                    <FileDirectoryIcon size={14} />
                  </div>
                </div>
                <span className="collections-tabs__tab-name">{collection.name}</span>
                <span className="collections-tabs__tab-count">
                  {getCollectionGameCount(collection.id)}
                </span>
              </button>

              <div className="collections-tabs__tab-actions">
                <button
                  type="button"
                  ref={(el) => (actionButtonRefs.current[collection.id] = el)}
                  className="collections-tabs__action-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDropdownToggle(collection.id);
                  }}
                >
                  <KebabHorizontalIcon size={16} />
                </button>
              </div>
            </div>
          ))}


        </div>
      </div>

      {/* Dropdown Portal */}
      {activeDropdown && createPortal(
        <>
          <div
            className="collections-tabs__dropdown-backdrop"
            onClick={() => setActiveDropdown(null)}
          />
          <div
            ref={dropdownRef}
            className="collections-tabs__dropdown"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
            }}
          >
            <button
              type="button"
              className="collections-tabs__dropdown-item"
              onClick={(e) => {
                e.stopPropagation();
                const collection = collections.find(c => c.id === activeDropdown);
                if (collection) handleEditCollection(collection);
              }}
            >
              <PencilIcon size={14} />
              {t("edit")}
            </button>
            <button
              type="button"
              className="collections-tabs__dropdown-item collections-tabs__dropdown-item--danger"
              onClick={(e) => {
                e.stopPropagation();
                const collection = collections.find(c => c.id === activeDropdown);
                if (collection) handleDeleteCollection(collection);
              }}
            >
              <TrashIcon size={14} />
              {t("delete")}
            </button>
          </div>
        </>,
        document.body
      )}

      {/* Modals */}
      {showEditModal && editingCollection && (
        <CollectionModal
          collection={editingCollection}
          onClose={handleCloseModals}
          onSave={handleCloseModals}
        />
      )}

      {showDeleteModal && editingCollection && (
        <DeleteCollectionModal
          collection={editingCollection}
          onClose={handleCloseModals}
          onDeleted={handleCloseModals}
        />
      )}
    </>
  );
}
