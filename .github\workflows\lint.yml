name: Lint

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on: pull_request

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Validate current commit (last commit) with commitlint
        run: npx commitlint --last --verbose

      - name: Typecheck
        run: yarn typecheck

      - name: Lint
        run: yarn lint

      - name: Format check
        run: yarn format-check
