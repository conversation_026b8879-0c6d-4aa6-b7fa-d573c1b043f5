import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  BookIcon,
  CheckIcon,
  DownloadIcon,
  ClockIcon,
  TrophyIcon,
  CalendarIcon
} from "@primer/octicons-react";

import type { LibraryGame } from "@types";
import { useFormat } from "@renderer/hooks";

import "./library-stats.scss";

interface LibraryStatsProps {
  games: LibraryGame[];
  filteredGames: LibraryGame[];
  className?: string;
  compact?: boolean;
}

export function LibraryStats({ games, filteredGames, className, compact = false }: LibraryStatsProps) {
  const { t } = useTranslation("library");
  const { numberFormatter } = useFormat();

  const stats = useMemo(() => {
    const totalGames = games.length;
    const installedGames = games.filter(game => Boolean(game.executablePath)).length;
    const notInstalledGames = totalGames - installedGames;
    
    const totalPlayTime = games.reduce((total, game) => {
      return total + (game.playTimeInMilliseconds || 0);
    }, 0);

    const totalPlayTimeHours = Math.floor(totalPlayTime / (1000 * 60 * 60));
    
    const recentlyPlayedGames = games.filter(game => {
      if (!game.lastTimePlayed) return false;
      const lastPlayed = new Date(game.lastTimePlayed);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return lastPlayed > thirtyDaysAgo;
    }).length;

    const mostPlayedGame = games.reduce((most, game) => {
      const gameTime = game.playTimeInMilliseconds || 0;
      const mostTime = most?.playTimeInMilliseconds || 0;
      return gameTime > mostTime ? game : most;
    }, null as LibraryGame | null);

    const recentlyAddedGames = games.filter(game => {
      if (!game.download?.timestamp) return false;
      const addedDate = new Date(game.download.timestamp);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return addedDate > sevenDaysAgo;
    }).length;

    return {
      totalGames,
      installedGames,
      notInstalledGames,
      totalPlayTimeHours,
      recentlyPlayedGames,
      mostPlayedGame,
      recentlyAddedGames,
      filteredCount: filteredGames.length,
    };
  }, [games, filteredGames]);

  const formatPlayTime = (milliseconds: number) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (compact) {
    return (
      <div className={`library-stats library-stats--compact ${className || ""}`}>
        <div className="library-stats__compact-content">
          <div className="library-stats__compact-stat">
            <BookIcon size={16} />
            <span>{numberFormatter.format(stats.totalGames)} {t("games")}</span>
          </div>
          <div className="library-stats__compact-stat">
            <CheckIcon size={16} />
            <span>{numberFormatter.format(stats.installedGames)} {t("installed")}</span>
          </div>
          <div className="library-stats__compact-stat">
            <ClockIcon size={16} />
            <span>{numberFormatter.format(stats.totalPlayTimeHours)}h {t("played")}</span>
          </div>
          {stats.filteredCount !== stats.totalGames && (
            <div className="library-stats__compact-filter">
              <span>{t("showing")} {numberFormatter.format(stats.filteredCount)}</span>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`library-stats ${className || ""}`}>
      <div className="library-stats__content">
        {/* Primary Stats */}
        <div className="library-stats__primary">
          <div className="library-stats__stat library-stats__stat--total">
            <div className="library-stats__stat-icon">
              <BookIcon size={20} />
            </div>
            <div className="library-stats__stat-content">
              <div className="library-stats__stat-value">
                {numberFormatter.format(stats.totalGames)}
              </div>
              <div className="library-stats__stat-label">
                {t("total_games")}
              </div>
            </div>
          </div>

          <div className="library-stats__stat library-stats__stat--installed">
            <div className="library-stats__stat-icon">
              <CheckIcon size={20} />
            </div>
            <div className="library-stats__stat-content">
              <div className="library-stats__stat-value">
                {numberFormatter.format(stats.installedGames)}
              </div>
              <div className="library-stats__stat-label">
                {t("installed")}
              </div>
            </div>
          </div>

          <div className="library-stats__stat library-stats__stat--playtime">
            <div className="library-stats__stat-icon">
              <ClockIcon size={20} />
            </div>
            <div className="library-stats__stat-content">
              <div className="library-stats__stat-value">
                {numberFormatter.format(stats.totalPlayTimeHours)}h
              </div>
              <div className="library-stats__stat-label">
                {t("total_playtime")}
              </div>
            </div>
          </div>
        </div>

        {/* Secondary Stats */}
        <div className="library-stats__secondary">
          <div className="library-stats__stat library-stats__stat--recent">
            <div className="library-stats__stat-icon">
              <CalendarIcon size={16} />
            </div>
            <div className="library-stats__stat-content">
              <div className="library-stats__stat-value">
                {numberFormatter.format(stats.recentlyPlayedGames)}
              </div>
              <div className="library-stats__stat-label">
                {t("recently_played")}
              </div>
            </div>
          </div>

          <div className="library-stats__stat library-stats__stat--new">
            <div className="library-stats__stat-icon">
              <DownloadIcon size={16} />
            </div>
            <div className="library-stats__stat-content">
              <div className="library-stats__stat-value">
                {numberFormatter.format(stats.recentlyAddedGames)}
              </div>
              <div className="library-stats__stat-label">
                {t("recently_added")}
              </div>
            </div>
          </div>

          {stats.mostPlayedGame && (
            <div className="library-stats__stat library-stats__stat--champion">
              <div className="library-stats__stat-icon">
                <TrophyIcon size={16} />
              </div>
              <div className="library-stats__stat-content">
                <div className="library-stats__stat-value">
                  {stats.mostPlayedGame.title}
                </div>
                <div className="library-stats__stat-label">
                  {formatPlayTime(stats.mostPlayedGame.playTimeInMilliseconds || 0)} • {t("most_played")}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Filter Results Indicator */}
        {stats.filteredCount !== stats.totalGames && (
          <div className="library-stats__filter-indicator">
            <span className="library-stats__filter-text">
              {t("showing")} {numberFormatter.format(stats.filteredCount)} {t("of")} {numberFormatter.format(stats.totalGames)} {t("games")}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
