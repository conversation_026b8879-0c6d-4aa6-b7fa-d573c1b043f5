import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { BookIcon, StarIcon, ClockIcon, DownloadIcon } from "@primer/octicons-react";

import { useLibrary } from "@renderer/hooks";
import { wasPlayedRecently } from "@renderer/utils/date-utils";

import "./sidebar-library.scss";

interface LibraryLinkProps {
  icon: React.ReactNode;
  label: string;
  count?: number;
  onClick: () => void;
}

function LibraryLink({ icon, label, count, onClick }: LibraryLinkProps) {
  return (
    <button
      type="button"
      className="sidebar-library__link"
      onClick={onClick}
    >
      <div className="sidebar-library__link-content">
        {icon}
        <span className="sidebar-library__link-label">{label}</span>
      </div>
      {count !== undefined && count > 0 && (
        <span className="sidebar-library__link-count">{count}</span>
      )}
    </button>
  );
}

export function SidebarLibrary() {
  const { t } = useTranslation("library");
  const { library } = useLibrary();
  const navigate = useNavigate();

  const handleLibraryNavigation = useCallback((collection?: string) => {
    if (collection) {
      navigate(`/library?collection=${collection}`);
    } else {
      navigate("/library");
    }
  }, [navigate]);

  // Calculate simple counts
  const favoriteCount = library.filter(game => game.favorite).length;
  const recentCount = library.filter(game => wasPlayedRecently(game.lastTimePlayed)).length;
  const installedCount = library.filter(game => Boolean(game.executablePath)).length;

  if (library.length === 0) {
    return null;
  }

  return (
    <section className="sidebar-library">
      <div className="sidebar-library__header">
        <BookIcon size={16} />
        <h3 className="sidebar-library__title">{t("library")}</h3>
      </div>

      <div className="sidebar-library__links">
        <LibraryLink
          icon={<BookIcon size={14} />}
          label={t("all_games")}
          count={library.length}
          onClick={() => handleLibraryNavigation()}
        />
        
        {favoriteCount > 0 && (
          <LibraryLink
            icon={<StarIcon size={14} />}
            label={t("favorites")}
            count={favoriteCount}
            onClick={() => handleLibraryNavigation("favorites")}
          />
        )}
        
        {recentCount > 0 && (
          <LibraryLink
            icon={<ClockIcon size={14} />}
            label={t("recently_played")}
            count={recentCount}
            onClick={() => handleLibraryNavigation("recently-played")}
          />
        )}
        
        {installedCount > 0 && (
          <LibraryLink
            icon={<DownloadIcon size={14} />}
            label={t("installed")}
            count={installedCount}
            onClick={() => handleLibraryNavigation("installed")}
          />
        )}
      </div>
    </section>
  );
}
