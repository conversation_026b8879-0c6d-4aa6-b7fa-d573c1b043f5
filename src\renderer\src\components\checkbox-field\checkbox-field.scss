@use "../../scss/globals.scss";

.checkbox-field {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: globals.$spacing-unit;
  cursor: pointer;

  &:has(input:disabled) {
    cursor: not-allowed;
    opacity: globals.$disabled-opacity;
  }

  &__checkbox {
    width: 20px;
    height: 20px;
    min-width: 20px;
    min-height: 20px;
    border-radius: 4px;
    background-color: globals.$dark-background-color;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: all ease 0.2s;
    border: solid 1px globals.$border-color;

    &:hover:not(:has(input:disabled)) {
      border-color: rgba(255, 255, 255, 0.5);
      background-color: rgba(255, 255, 255, 0.05);
    }

    &.checked {
      background-color: globals.$success-color;
      border-color: globals.$success-color;

      &:hover:not(:has(input:disabled)) {
        background-color: rgba(28, 151, 73, 0.9);
        border-color: rgba(28, 151, 73, 0.9);
      }

      svg {
        color: white;
      }
    }
  }

  &__input {
    width: 100%;
    height: 100%;
    position: absolute;
    margin: 0;
    padding: 0;
    opacity: 0;
    cursor: pointer;

    &:disabled {
      cursor: not-allowed;
    }
  }

  &__label {
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;

    &:has(+ input:disabled) {
      cursor: not-allowed;
    }
  }
}
