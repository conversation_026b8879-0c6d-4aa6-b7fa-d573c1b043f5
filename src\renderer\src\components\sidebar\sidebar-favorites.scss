@use "../../scss/globals.scss";

.sidebar-favorites {
  margin-bottom: calc(globals.$spacing-unit * 2);

  &__header {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit / 2);
    margin-bottom: calc(globals.$spacing-unit);
    padding-bottom: calc(globals.$spacing-unit / 2);
    border-bottom: 1px solid globals.$border-color;
  }

  &__title {
    font-size: 14px;
    font-weight: 600;
    color: globals.$muted-color;
    margin: 0;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 4);
  }

  &__game {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(globals.$spacing-unit / 2) calc(globals.$spacing-unit);
    background: transparent;
    border: none;
    border-radius: 6px;
    color: globals.$muted-color;
    text-align: left;
    cursor: pointer;
    transition: all 0.15s ease;
    width: 100%;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
      color: white;
    }

    &--active {
      background-color: globals.$color-primary;
      color: white;

      .sidebar-favorites__game-icon {
        color: white;
      }
    }
  }

  &__game-title {
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }

  &__game-icon {
    color: globals.$muted-color;
    flex-shrink: 0;
    margin-left: calc(globals.$spacing-unit / 2);
  }
}
