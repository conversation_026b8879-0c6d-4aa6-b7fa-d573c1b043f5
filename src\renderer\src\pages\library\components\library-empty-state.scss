@use "../../../scss/globals.scss";

.library-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: calc(globals.$spacing-unit * 6);
  animation: fadeIn 0.6s ease-out;

  &__content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: calc(globals.$spacing-unit * 3);
    max-width: 500px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: calc(globals.$spacing-unit * 6);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 96px;
    height: 96px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    margin-bottom: calc(globals.$spacing-unit * 2);
    animation: iconFloat 3s ease-in-out infinite;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    svg {
      color: globals.$body-color;
      opacity: 0.8;
    }
  }

  &__title {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: globals.$muted-color;
    line-height: 1.3;
    background: linear-gradient(135deg, globals.$muted-color 0%, rgba(255, 255, 255, 0.8) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.01em;
  }

  &__subtitle {
    margin: 0;
    font-size: globals.$body-font-size;
    color: globals.$body-color;
    line-height: 1.5;
    opacity: 0.9;
  }

  &__action {
    margin-top: calc(globals.$spacing-unit * 1.5);
  }
}



// Enhanced animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}
