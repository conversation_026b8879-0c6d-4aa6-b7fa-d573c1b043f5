@use "../../../scss/globals.scss";

.library-toast {
  position: fixed;
  top: calc(globals.$spacing-unit * 3);
  right: calc(globals.$spacing-unit * 3);
  z-index: 1000;
  min-width: 300px;
  max-width: 500px;
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2);
  animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);

  &--success {
    background: linear-gradient(135deg, rgba(28, 151, 73, 0.9), rgba(28, 151, 73, 0.7));
    border-color: rgba(28, 151, 73, 0.3);

    .library-toast__icon {
      color: white;
    }
  }

  &--error {
    background: linear-gradient(135deg, rgba(128, 29, 30, 0.9), rgba(128, 29, 30, 0.7));
    border-color: rgba(128, 29, 30, 0.3);

    .library-toast__icon {
      color: white;
    }
  }

  &--warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.9), rgba(255, 193, 7, 0.7));
    border-color: rgba(255, 193, 7, 0.3);

    .library-toast__icon {
      color: rgba(0, 0, 0, 0.8);
    }

    .library-toast__message {
      color: rgba(0, 0, 0, 0.8);
    }

    .library-toast__close {
      color: rgba(0, 0, 0, 0.6);

      &:hover {
        color: rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }

  &__content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  &__message {
    flex: 1;
    color: white;
    font-size: globals.$body-font-size;
    font-weight: 500;
    line-height: 1.4;
  }

  &__close {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: calc(globals.$spacing-unit / 2);
    border-radius: 6px;
    transition: all ease 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    &:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// Exit animation
.library-toast.library-toast--exiting {
  animation: slideOutRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(100%) scale(0.9);
  }
}
