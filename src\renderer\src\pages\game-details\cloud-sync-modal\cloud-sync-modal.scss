@use "../../../scss/globals.scss";

@keyframes rotate {
  0% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

.cloud-sync-modal {
  &__header {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__title-container {
    display: flex;
    gap: 4px;
    flex-direction: column;
  }

  &__backups-header {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: globals.$spacing-unit;
  }

  &__artifact-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: globals.$body-color;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    padding: 0;
    background-color: transparent;
    border: none;
    cursor: pointer;
  }

  &__artifacts {
    display: flex;
    gap: globals.$spacing-unit;
    flex-direction: column;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__artifact {
    display: flex;
    text-align: left;
    flex-direction: row;
    align-items: center;
    gap: globals.$spacing-unit;
    color: globals.$body-color;
    padding: calc(globals.$spacing-unit * 2);
    background-color: globals.$dark-background-color;
    border: 1px solid globals.$border-color;
    border-radius: 4px;
    justify-content: space-between;

    &-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    &-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
    }

    &-meta {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }

  &__sync-icon {
    animation: rotate 1s linear infinite;
  }

  &__progress {
    width: 100%;
    height: 5px;

    &::-webkit-progress-bar {
      background-color: globals.$dark-background-color;
    }

    &::-webkit-progress-value {
      background-color: globals.$muted-color;
    }
  }

  &__manage-files-button {
    margin: 0;
    padding: 0;
    align-self: flex-start;
    font-size: 14px;
    cursor: pointer;
    text-decoration: underline;
    color: globals.$body-color;

    &:disabled {
      cursor: not-allowed;
      opacity: globals.$disabled-opacity;
    }
  }

  &__backup-state-label {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
