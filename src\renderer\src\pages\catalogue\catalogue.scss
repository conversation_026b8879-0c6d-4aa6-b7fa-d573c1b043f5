@use "../../scss/globals.scss";

.catalogue {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: calc(globals.$spacing-unit * 2);
  width: 100%;
  padding: 16px;
  scroll-behavior: smooth;

  &__filters-container {
    width: 270px;
    min-width: 270px;
    max-width: 270px;
    background-color: globals.$dark-background-color;
    border-radius: 4px;
    padding: 16px;
    border: 1px solid globals.$border-color;
    align-self: flex-start;
  }

  &__header {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }

  &__filters-wrapper {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  &__filters-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__content {
    display: flex;
    gap: calc(globals.$spacing-unit * 2);
    justify-content: space-between;
  }

  &__games-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  &__pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
  }

  &__result-count {
    font-size: 12px;
  }

  &__filters-sections {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &__skeleton {
    height: 105px;
    border-radius: 4px;
    border: solid 1px rgba(255, 255, 255, 0.15);
  }
}
