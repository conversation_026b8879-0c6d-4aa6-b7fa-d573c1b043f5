import { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  BookIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FileDirectoryIcon
} from "@primer/octicons-react";

import { useAppDispatch, useAppSelector, useLibrary } from "@renderer/hooks";
import { setSelectedCollection } from "@renderer/features/library-collections-slice";
import { CollectionModal } from "./collection-modal";
import { DeleteCollectionModal } from "./delete-collection-modal";

import type { GameCollection } from "@types";

import "./collections-list.scss";

export function CollectionsList() {
  const { t } = useTranslation("library");
  const dispatch = useAppDispatch();
  const { library } = useLibrary();

  const { collections, selectedCollection } = useAppSelector(
    (state) => state.libraryCollections
  );

  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingCollection, setEditingCollection] = useState<GameCollection | null>(null);

  const handleCollectionSelect = useCallback(
    (collectionId: string | null) => {
      dispatch(setSelectedCollection(collectionId));
    },
    [dispatch]
  );

  const getCollectionGameCount = useCallback(
    (collectionId: string) => {
      const collection = collections.find((c) => c.id === collectionId);
      if (!collection) return 0;

      // Count games that actually exist in the library
      return collection.gameIds.filter((gameId) =>
        library.some((game) => game.id === gameId)
      ).length;
    },
    [collections, library]
  );

  const handleEditCollection = useCallback((collection: GameCollection) => {
    setEditingCollection(collection);
    setShowEditModal(true);
  }, []);

  const handleDeleteCollection = useCallback((collection: GameCollection) => {
    setEditingCollection(collection);
    setShowDeleteModal(true);
  }, []);

  const handleCloseModals = useCallback(() => {
    setShowEditModal(false);
    setShowDeleteModal(false);
    setEditingCollection(null);
  }, []);

  return (
    <div className="collections-list">
      <div className="collections-list__header">
        <h3 className="collections-list__title">{t("collections")}</h3>
      </div>

      <div className="collections-list__content">
        {/* All Games - Default view */}
        <button
          type="button"
          className={`collections-list__item ${
            selectedCollection === null ? "collections-list__item--active" : ""
          }`}
          onClick={() => handleCollectionSelect(null)}
        >
          <div className="collections-list__item-icon">
            <BookIcon size={16} />
          </div>
          <div className="collections-list__item-content">
            <span className="collections-list__item-name">{t("all_games")}</span>
            <span className="collections-list__item-count">
              {library.length}
            </span>
          </div>
        </button>

        {/* User Collections */}
        {collections.length > 0 && (
          <>
            <div className="collections-list__separator">
              <span>{t("my_collections")}</span>
            </div>

            {collections.map((collection) => (
              <div
                key={collection.id}
                className={`collections-list__item-container ${
                  selectedCollection === collection.id 
                    ? "collections-list__item-container--active" 
                    : ""
                }`}
              >
                <button
                  type="button"
                  className="collections-list__item"
                  onClick={() => handleCollectionSelect(collection.id)}
                >
                  <div className="collections-list__item-icon">
                    <div
                      className="collections-list__item-color-main"
                      style={{ backgroundColor: collection.color }}
                    >
                      <FileDirectoryIcon size={16} />
                    </div>
                  </div>
                  <div className="collections-list__item-content">
                    <span className="collections-list__item-name">
                      {collection.name}
                    </span>
                    <span className="collections-list__item-count">
                      {getCollectionGameCount(collection.id)}
                    </span>
                  </div>
                </button>

                <div className="collections-list__item-actions">
                  <button
                    type="button"
                    className="collections-list__action-button"
                    title={t("edit_collection")}
                    onClick={() => handleEditCollection(collection)}
                  >
                    <PencilIcon size={12} />
                  </button>
                  <button
                    type="button"
                    className="collections-list__action-button collections-list__action-button--danger"
                    title={t("delete_collection")}
                    onClick={() => handleDeleteCollection(collection)}
                  >
                    <TrashIcon size={12} />
                  </button>
                </div>
              </div>
            ))}
          </>
        )}

        {/* Empty state */}
        {collections.length === 0 && (
          <div className="collections-list__empty">
            <FileDirectoryIcon size={24} />
            <p>{t("no_collections")}</p>
            <p className="collections-list__empty-hint">
              {t("create_collection_hint")}
            </p>
          </div>
        )}
      </div>

      {/* Modals */}
      {showEditModal && editingCollection && (
        <CollectionModal
          collection={editingCollection}
          onClose={handleCloseModals}
          onSave={handleCloseModals}
        />
      )}

      {showDeleteModal && editingCollection && (
        <DeleteCollectionModal
          collection={editingCollection}
          onClose={handleCloseModals}
          onDeleted={handleCloseModals}
        />
      )}
    </div>
  );
}
