import { useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { StarIcon, DownloadIcon } from "@primer/octicons-react";

import type { LibraryGame } from "@types";
import { useLibrary, useDownload } from "@renderer/hooks";
import { buildGameDetailsPath } from "@renderer/helpers";

import "./sidebar-favorites.scss";

interface FavoriteGameProps {
  game: LibraryGame;
  onClick: (game: LibraryGame) => void;
  isDownloading?: boolean;
}

function FavoriteGame({ game, onClick, isDownloading }: FavoriteGameProps) {
  const location = useLocation();
  
  const isActive = location.pathname === buildGameDetailsPath({
    ...game,
    objectId: game.objectId,
  });

  return (
    <button
      type="button"
      className={`sidebar-favorites__game ${
        isActive ? "sidebar-favorites__game--active" : ""
      }`}
      onClick={() => onClick(game)}
      title={game.title}
    >
      <span className="sidebar-favorites__game-title">
        {game.title}
      </span>
      {isDownloading && (
        <DownloadIcon size={12} className="sidebar-favorites__game-icon" />
      )}
    </button>
  );
}

export function SidebarFavorites() {
  const { t } = useTranslation("library");
  const { library } = useLibrary();
  const { lastPacket } = useDownload();
  const navigate = useNavigate();
  const location = useLocation();

  const handleGameClick = useCallback((game: LibraryGame) => {
    const path = buildGameDetailsPath({
      ...game,
      objectId: game.objectId,
    });
    if (path !== location.pathname) {
      navigate(path);
    }
  }, [navigate, location.pathname]);

  // Get favorite games, sorted by most recently played
  const favoriteGames = useMemo(() => {
    return library
      .filter(game => game.favorite)
      .sort((a, b) => {
        const aTime = a.lastTimePlayed ? new Date(a.lastTimePlayed).getTime() : 0;
        const bTime = b.lastTimePlayed ? new Date(b.lastTimePlayed).getTime() : 0;
        return bTime - aTime;
      })
      .slice(0, 8); // Limit to 8 favorites
  }, [library]);

  // Get currently downloading games
  const downloadingGameIds = useMemo(() => {
    return new Set(
      library
        .filter(game => game.download?.status === "active" || game.download?.status === "paused")
        .map(game => game.id)
    );
  }, [library]);

  if (favoriteGames.length === 0) {
    return null;
  }

  return (
    <section className="sidebar-favorites">
      <div className="sidebar-favorites__header">
        <StarIcon size={16} />
        <h3 className="sidebar-favorites__title">{t("favorites")}</h3>
      </div>

      <div className="sidebar-favorites__list">
        {favoriteGames.map((game) => (
          <FavoriteGame
            key={game.id}
            game={game}
            onClick={handleGameClick}
            isDownloading={downloadingGameIds.has(game.id)}
          />
        ))}
      </div>
    </section>
  );
}
