/**
 * Utility functions for handling dates in the library
 */

/**
 * Safely converts a date value to timestamp
 * Handles Date objects, date strings, and null values
 */
export function getTimestamp(date: Date | string | null | undefined): number | null {
  if (!date) return null;
  
  try {
    if (date instanceof Date) {
      const timestamp = date.getTime();
      return isNaN(timestamp) ? null : timestamp;
    }
    
    if (typeof date === 'string') {
      const timestamp = new Date(date).getTime();
      return isNaN(timestamp) ? null : timestamp;
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Checks if a game was played recently (within the last 7 days)
 */
export function wasPlayedRecently(lastTimePlayed: Date | string | null | undefined): boolean {
  const timestamp = getTimestamp(lastTimePlayed);
  if (!timestamp) return false;
  
  const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;
  return Date.now() - timestamp < sevenDaysInMs;
}

/**
 * Formats a date for display
 */
export function formatLastPlayed(lastTimePlayed: Date | string | null | undefined): string | null {
  const timestamp = getTimestamp(lastTimePlayed);
  if (!timestamp) return null;
  
  return new Date(timestamp).toLocaleDateString();
}
