@use "../../../scss/globals.scss";

.download-settings-modal {
  &__container {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 3);
    width: 100%;
  }

  &__downloads-path-field {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
  }

  &__hint-text {
    font-size: 12px;
    color: globals.$body-color;
  }

  &__downloaders {
    display: grid;
    gap: globals.$spacing-unit;
    grid-template-columns: repeat(2, 1fr);
  }

  &__downloader-option {
    position: relative;

    &:only-child {
      grid-column: 1 / -1;
    }
  }

  &__downloader-icon {
    position: absolute;
    left: calc(globals.$spacing-unit * 2);
  }

  &__path-error {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  &__change-path-button {
    align-self: flex-end;
  }
}
