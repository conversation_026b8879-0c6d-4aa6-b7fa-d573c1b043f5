import { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { createPortal } from "react-dom";
import { XIcon, AlertIcon, TrashIcon } from "@primer/octicons-react";

import { Button } from "@renderer/components";
import { useLibraryCollections, useToast } from "@renderer/hooks";

import type { GameCollection } from "@types";

import "./delete-collection-modal.scss";

interface DeleteCollectionModalProps {
  collection: GameCollection;
  onClose: () => void;
  onDeleted?: () => void;
}

export function DeleteCollectionModal({
  collection,
  onClose,
  onDeleted
}: DeleteCollectionModalProps) {
  const { t } = useTranslation("library");
  const { deleteCollection } = useLibraryCollections();
  const { showSuccessToast, showErrorToast } = useToast();

  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = useCallback(async () => {
    try {
      setIsLoading(true);

      await deleteCollection(collection.id);

      showSuccessToast(t("collection_deleted_successfully"));

      onDeleted?.();
      onClose();
    } catch (error) {
      console.error("Failed to delete collection:", error);
      showErrorToast(t("collection_delete_failed"));
    } finally {
      setIsLoading(false);
    }
  }, [collection.id, deleteCollection, onDeleted, onClose, t, showSuccessToast, showErrorToast]);

  return createPortal(
    <div className="delete-collection-modal-backdrop" onClick={onClose}>
      <div className="delete-collection-modal" onClick={(e) => e.stopPropagation()}>
        <div className="delete-collection-modal__header">
          <div className="delete-collection-modal__icon">
            <AlertIcon size={24} />
          </div>
          <h2 className="delete-collection-modal__title">
            {t("delete_collection_title")}
          </h2>
          <button
            type="button"
            className="delete-collection-modal__close"
            onClick={onClose}
          >
            <XIcon size={20} />
          </button>
        </div>

        <div className="delete-collection-modal__content">
          <div className="delete-collection-modal__collection-preview">
            <div 
              className="delete-collection-modal__collection-color"
              style={{ backgroundColor: collection.color }}
            />
            <div className="delete-collection-modal__collection-info">
              <h3 className="delete-collection-modal__collection-name">
                {collection.name}
              </h3>
              {collection.description && (
                <p className="delete-collection-modal__collection-description">
                  {collection.description}
                </p>
              )}
            </div>
          </div>

          <p className="delete-collection-modal__message">
            {t("delete_collection_message", { name: collection.name })}
          </p>

          <div className="delete-collection-modal__warning">
            <AlertIcon size={16} />
            <span>{t("delete_collection_warning")}</span>
          </div>
        </div>

        <div className="delete-collection-modal__footer">
          <div className="delete-collection-modal__actions">
            <Button onClick={onClose} theme="outline" disabled={isLoading}>
              {t("cancel")}
            </Button>
            <Button
              onClick={handleDelete}
              theme="danger"
              disabled={isLoading}
              loading={isLoading}
            >
              <TrashIcon size={16} />
              {isLoading ? t("deleting") : t("delete")}
            </Button>
          </div>

        </div>
      </div>
    </div>,
    document.body
  );
}
