@use "../../../scss/globals.scss";

.user-stats {
  &__box {
    background-color: globals.$background-color;
    border-radius: 4px;
    border: solid 1px globals.$border-color;
    padding: calc(globals.$spacing-unit * 2);
  }

  &__section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: calc(globals.$spacing-unit * 2);
  }

  &__list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__list-item {
    display: flex;
    flex-direction: column;
    transition: all ease 0.1s;
    color: globals.$muted-color;
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
    padding: globals.$spacing-unit;
    gap: globals.$spacing-unit;

    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
      text-decoration: none;
    }
  }

  &__list-title {
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__list-description {
    display: flex;
    align-items: center;
    gap: globals.$spacing-unit;
  }

  &__stats-row {
    display: flex;
    justify-content: space-between;
  }

  &__link {
    text-align: start;
    color: globals.$body-color;

    &--warning {
      color: globals.$warning-color;
    }

    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }
}
