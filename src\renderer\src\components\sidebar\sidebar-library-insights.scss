@use "../../scss/globals.scss";

.sidebar-library-insights {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  padding: calc(globals.$spacing-unit * 2);
  backdrop-filter: blur(24px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  margin-bottom: calc(globals.$spacing-unit * 2);

  // Animation for smooth appearance
  animation: insightsFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &__header {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    margin-bottom: calc(globals.$spacing-unit * 2);
    padding-bottom: calc(globals.$spacing-unit);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  &__title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__section {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit);
  }

  &__section-title {
    margin: 0;
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  // Statistics Cards
  &__stats {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit);
  }

  &__card {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    padding: calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.06);
    border-radius: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 48px;

    &--clickable {
      cursor: pointer;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75);
    }
  }

  &__card-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 36px;
      height: 36px;
      min-width: 36px;
      min-height: 36px;
    }
  }

  &__card-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  &__card-value {
    font-size: 18px;
    font-weight: 700;
    color: globals.$body-color;
    line-height: 1;
  }

  &__card-title {
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  &__card-subtitle {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
  }

  &__card-arrow {
    color: rgba(255, 255, 255, 0.4);
    transition: all 0.2s ease;
  }

  &__card--clickable:hover &__card-arrow {
    color: rgba(255, 255, 255, 0.7);
    transform: translateX(2px);
  }

  // Quick Collections
  &__collections {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 2);
  }

  &__collection {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    padding: calc(globals.$spacing-unit) calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.06);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 44px;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 48px;
      padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.75);
    }
  }

  &__collection-icon {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 28px;
      height: 28px;
      min-width: 28px;
      min-height: 28px;
    }
  }

  &__collection-content {
    flex: 1;
    min-width: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__collection-name {
    font-size: 13px;
    font-weight: 500;
    color: globals.$body-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__collection-count {
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.08);
    padding: 2px 6px;
    border-radius: 4px;
    min-width: 20px;
    text-align: center;
  }

  // Library Health
  &__health {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit);
  }

  &__health-item {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
  }

  &__health-label {
    font-size: 11px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    min-width: 50px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  &__health-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 3px;
    overflow: hidden;
  }

  &__health-progress {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  &__health-value {
    font-size: 11px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    min-width: 30px;
    text-align: right;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding: calc(globals.$spacing-unit * 1.5);
    border-radius: 12px;
    
    &__card {
      min-height: 44px;
      padding: calc(globals.$spacing-unit);
    }

    &__card-icon {
      width: 28px;
      height: 28px;
      min-width: 28px;
      min-height: 28px;
    }

    &__card-value {
      font-size: 16px;
    }

    &__collection {
      min-height: 40px;
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit);
    }

    &__collection-icon {
      width: 20px;
      height: 20px;
      min-width: 20px;
      min-height: 20px;
    }
  }
}

@keyframes insightsFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
