@use "../../scss/globals.scss";

.game-card {
  width: 100%;
  height: 180px;
  box-shadow: 0px 0px 15px 0px #000000;
  overflow: hidden;
  border-radius: 4px;
  transition: all ease 0.2s;
  border: solid 1px globals.$border-color;
  cursor: pointer;
  z-index: 1;

  &:active {
    opacity: globals.$active-opacity;
  }

  &__backdrop {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.7) 50%, transparent 100%);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    position: relative;
  }

  &__cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    position: absolute;
    z-index: -1;
    transition: all ease 0.2s;
  }

  &__content {
    color: #dadbe1;
    padding: globals.$spacing-unit calc(globals.$spacing-unit * 2);
    display: flex;
    align-items: flex-start;
    gap: globals.$spacing-unit;
    flex-direction: column;
    transition: all ease 0.2s;
    transform: translateY(24px);
  }

  &__title {
    font-size: 16px;
    font-weight: bold;
    text-align: left;
  }

  &__download-options {
    display: flex;
    margin: 0;
    padding: 0;
    gap: globals.$spacing-unit;
    flex-wrap: wrap;
    list-style: none;
  }

  &__specifics {
    display: flex;
    gap: calc(globals.$spacing-unit * 2);
    justify-content: center;
  }

  &__specifics-item {
    gap: globals.$spacing-unit;
    display: flex;
    color: globals.$muted-color;
    font-size: 12px;
    align-items: flex-end;
  }

  &__title-container {
    display: flex;
    align-items: center;
    gap: globals.$spacing-unit;
    color: globals.$muted-color;
  }

  &__shop-icon {
    width: 20px;
    height: 20px;
    min-width: 20px;
    z-index: 0; // Ensure shop icon doesn't interfere with dropdown menus
  }

  &__no-download-label {
    color: globals.$body-color;
    font-weight: bold;
  }

  &:hover &__cover {
    transform: scale(1.05);
  }
  &:hover &__content {
    transform: translateY(0px);
  }
}
