@use "../../scss/globals.scss";

.achievement-panel {
  width: 100%;
  padding: globals.$spacing-unit * 2 globals.$spacing-unit * 3;
  background-color: globals.$background-color;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: space-between;
  border-bottom: solid 1px globals.$border-color;

  &__content {
    display: flex;
    gap: globals.$spacing-unit;
    justify-content: center;
    align-items: center;
  }

  &__actions {
    display: flex;
    gap: globals.$spacing-unit;
  }

  &__download-details-row {
    gap: globals.$spacing-unit;
    display: flex;
    color: globals.$body-color;
    align-items: center;
  }

  &__downloads-link {
    color: globals.$body-color;
    text-decoration: underline;
  }

  &__progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    transition: all ease 0.2s;

    &::-webkit-progress-bar {
      background-color: transparent;
    }

    &::-webkit-progress-value {
      background-color: globals.$muted-color;
    }

    &--disabled {
      opacity: globals.$disabled-opacity;
    }
  }

  &__link {
    text-align: start;
    color: globals.$body-color;
    background: none;
    border: none;
    padding: 0;

    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }

    &--warning {
      color: globals.$warning-color;
    }
  }

  &__grid {
    display: grid;
    gap: globals.$spacing-unit * 2;
  }

  &__grid--with-subscription {
    grid-template-columns: 3fr 1fr 1fr;
  }

  &__grid--without-subscription {
    grid-template-columns: 3fr 2fr;
  }

  &__points-container {
    display: flex;
    gap: globals.$spacing-unit;
  }

  &__content-icon {
    width: 18px;
    height: 18px;
  }
}
