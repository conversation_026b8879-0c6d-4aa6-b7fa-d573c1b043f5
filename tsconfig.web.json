{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/env.d.ts", "src/renderer/src/**/*", "src/renderer/src/**/*.tsx", "src/preload/*.d.ts", "src/locales/index.ts", "src/shared/**/*", "src/stories/**/*", ".storybook/**/*"], "compilerOptions": {"composite": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@renderer/*": ["src/renderer/src/*"], "@types": ["src/types/index.ts"], "@locales": ["src/locales/index.ts"], "@shared": ["src/shared/index.ts"]}}}