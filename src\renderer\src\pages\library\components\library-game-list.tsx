import { useCallback, useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { PlayIcon, ClockIcon, KebabHorizontalIcon, PlusIcon, DashIcon, LinkExternalIcon } from "@primer/octicons-react";

import { buildGameDetailsPath } from "@renderer/helpers";
import { useFormat, useLibraryCollections } from "@renderer/hooks";
import type { LibraryGame } from "@types";

import SteamLogo from "@renderer/assets/steam-logo.svg?react";

import "./library-game-list.scss";

interface LibraryGameListProps {
  games: LibraryGame[];
  onAddToCollection?: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
}

interface GameListItemProps {
  game: LibraryGame;
  onGameClick: (game: LibraryGame) => void;
  onAddToCollection: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
}

function GameListItem({ game, onGameClick, onAddToCollection, onRemoveFromCollection }: GameListItemProps) {
  const { t } = useTranslation("library");
  const { numberFormatter } = useFormat();
  const { selectedCollection, isGameInCollection } = useLibraryCollections();
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const optionsRef = useRef<HTMLDivElement>(null);

  // Check if game is in the currently selected collection
  const isInCurrentCollection = selectedCollection ? isGameInCollection(game.id, selectedCollection) : false;

  const formatPlayTime = useCallback(
    (playTimeInMilliseconds: number) => {
      const hours = playTimeInMilliseconds / (1000 * 60 * 60);
      if (hours < 1) {
        const minutes = Math.floor(playTimeInMilliseconds / (1000 * 60));
        return `${minutes}m`;
      }
      return `${numberFormatter.format(hours)}h`;
    },
    [numberFormatter]
  );

  const isGamePlayable = useCallback(
    (game: LibraryGame) => Boolean(game.executablePath),
    []
  );

  const handleOptionsClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setShowOptionsMenu(!showOptionsMenu);
  }, [showOptionsMenu]);

  const handleGoToGamePage = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setShowOptionsMenu(false);
    onGameClick(game);
  }, [game, onGameClick]);

  const handleAddToCollection = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setShowOptionsMenu(false);
    onAddToCollection(game);
  }, [game, onAddToCollection]);

  const handleRemoveFromCollection = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setShowOptionsMenu(false);
    onRemoveFromCollection?.(game);
  }, [game, onRemoveFromCollection]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (optionsRef.current && !optionsRef.current.contains(event.target as Node)) {
        setShowOptionsMenu(false);
      }
    };

    if (showOptionsMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showOptionsMenu]);

  return (
    <button
      type="button"
      className="library-game-list__item"
      onClick={() => onGameClick(game)}
    >
      {/* Game info column */}
      <div className="library-game-list__game-info">
        <div className="library-game-list__game-image">
          {game.iconUrl ? (
            <img
              src={game.iconUrl}
              alt={game.title}
              className="library-game-list__image"
              loading="lazy"
            />
          ) : (
            <div className="library-game-list__image-placeholder">
              <SteamLogo className="library-game-list__placeholder-icon" />
            </div>
          )}
        </div>
        <div className="library-game-list__game-details">
          <div className="library-game-list__title-container">
            <SteamLogo className="library-game-list__shop-icon" />
            <h3 className="library-game-list__title">{game.title}</h3>
          </div>
        </div>
      </div>

      {/* Playtime column */}
      <div className="library-game-list__playtime">
        <ClockIcon size={12} />
        {formatPlayTime(game.playTimeInMilliseconds)}
      </div>

      {/* Last played column */}
      <div className="library-game-list__last-played">
        {game.lastTimePlayed
          ? new Date(game.lastTimePlayed).toLocaleDateString()
          : t("never")}
      </div>

      {/* Status column */}
      <div className="library-game-list__status">
        {isGamePlayable(game) ? (
          <span className="library-game-list__status-badge library-game-list__status-badge--playable">
            <PlayIcon size={12} />
            {t("playable")}
          </span>
        ) : (
          <span className="library-game-list__status-badge library-game-list__status-badge--not-installed">
            {t("not_installed")}
          </span>
        )}
      </div>

      {/* Options column */}
      <div className="library-game-list__options" ref={optionsRef}>
        <button
          type="button"
          className="library-game-list__options-button"
          onClick={handleOptionsClick}
        >
          <KebabHorizontalIcon size={14} />
        </button>

        {showOptionsMenu && (
          <div className="library-game-list__options-menu">
            <button
              type="button"
              className="library-game-list__options-item"
              onClick={handleGoToGamePage}
            >
              <LinkExternalIcon size={14} />
              {t("go_to_game_page")}
            </button>
            {selectedCollection && isInCurrentCollection ? (
              <button
                type="button"
                className="library-game-list__options-item"
                onClick={handleRemoveFromCollection}
              >
                <DashIcon size={14} />
                {t("remove_from_collection")}
              </button>
            ) : (
              <button
                type="button"
                className="library-game-list__options-item"
                onClick={handleAddToCollection}
              >
                <PlusIcon size={14} />
                {t("add_to_collection")}
              </button>
            )}
          </div>
        )}
      </div>
    </button>
  );
}

export function LibraryGameList({ games, onAddToCollection, onRemoveFromCollection }: LibraryGameListProps) {
  const { t } = useTranslation("library");
  const navigate = useNavigate();
  const { numberFormatter } = useFormat();

  if (games.length === 0) {
    return null; // Empty state is handled by parent component
  }

  const handleGameClick = useCallback(
    (game: LibraryGame) => {
      navigate(buildGameDetailsPath(game));
    },
    [navigate]
  );

  const formatPlayTime = useCallback(
    (playTimeInMilliseconds: number) => {
      const hours = playTimeInMilliseconds / (1000 * 60 * 60);
      if (hours < 1) {
        const minutes = Math.floor(playTimeInMilliseconds / (1000 * 60));
        return `${minutes} ${t("minutes")}`;
      }
      return `${numberFormatter.format(hours)} ${t("hours")}`;
    },
    [numberFormatter, t]
  );

  const handleAddToCollection = useCallback(
    (game: LibraryGame) => {
      onAddToCollection?.(game);
    },
    [onAddToCollection]
  );

  const handleRemoveFromCollection = useCallback(
    (game: LibraryGame) => {
      onRemoveFromCollection?.(game);
    },
    [onRemoveFromCollection]
  );

  if (games.length === 0) {
    return (
      <div className="library-game-list__empty">
        <p>{t("no_games_found")}</p>
      </div>
    );
  }

  return (
    <div className="library-game-list">
      <div className="library-game-list__header">
        <div className="library-game-list__header-cell library-game-list__header-cell--game">
          {t("game")}
        </div>
        <div className="library-game-list__header-cell library-game-list__header-cell--playtime">
          {t("playtime")}
        </div>
        <div className="library-game-list__header-cell library-game-list__header-cell--last-played">
          {t("last_played")}
        </div>
        <div className="library-game-list__header-cell library-game-list__header-cell--status">
          {t("status")}
        </div>
        <div className="library-game-list__header-cell">
          {/* Options column header - empty */}
        </div>
      </div>

      <div className="library-game-list__content">
        {games.map((game) => (
          <GameListItem
            key={game.id}
            game={game}
            onGameClick={handleGameClick}
            onAddToCollection={handleAddToCollection}
            onRemoveFromCollection={handleRemoveFromCollection}
          />
        ))}
      </div>
    </div>
  );
}
