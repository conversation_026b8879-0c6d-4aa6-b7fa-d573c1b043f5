{"extends": "@electron-toolkit/tsconfig/tsconfig.node.json", "include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*", "src/locales/index.ts", "src/shared/**/*", "src/types/**/*"], "compilerOptions": {"module": "ESNext", "composite": true, "types": ["electron-vite/node"], "baseUrl": ".", "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "paths": {"@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/*"], "@types": ["src/types/index.ts"], "@locales": ["src/locales/index.ts"], "@resources": ["src/resources/index.ts"], "@shared": ["src/shared/index.ts"]}}}