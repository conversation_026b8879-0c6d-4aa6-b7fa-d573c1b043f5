import { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  AppsIcon,
  FileDirectoryIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  XIcon,
  ClockIcon,
  StarIcon,
  DownloadIcon,
  PlayIcon,
} from "@primer/octicons-react";

import { Button } from "@renderer/components";
import { useLibrary } from "@renderer/hooks";
import { wasPlayedRecently } from "@renderer/utils/date-utils";

import type { GameCollection, LibraryGame } from "@types";

import "./library-collections-sidebar.scss";

interface LibraryCollectionsSidebarProps {
  collections: GameCollection[];
  selectedCollection: string | null;
  library: LibraryGame[];
  isVisible: boolean;
  onSelectCollection: (collectionId: string | null) => void;
  onCreateCollection: () => void;
  onEditCollection: (collection: GameCollection) => void;
  onDeleteCollection: (collection: GameCollection) => void;
  onClose: () => void;
}

export function LibraryCollectionsSidebar({
  collections,
  selectedCollection,
  library,
  isVisible,
  onSelectCollection,
  onCreateCollection,
  onEditCollection,
  onDeleteCollection,
  onClose,
}: LibraryCollectionsSidebarProps) {
  const { t } = useTranslation("library");
  const [hoveredCollection, setHoveredCollection] = useState<string | null>(null);

  const getCollectionGameCount = useCallback((collectionId: string) => {
    const collection = collections.find(c => c.id === collectionId);
    return collection ? collection.gameIds.length : 0;
  }, [collections]);

  // Smart collections data
  const smartCollections = [
    {
      id: "recently-played",
      name: t("recently_played"),
      icon: <ClockIcon size={20} />,
      count: library.filter(game => wasPlayedRecently(game.lastTimePlayed)).length,
      color: "#3e62c0",
    },
    {
      id: "favorites",
      name: t("favorites"),
      icon: <StarIcon size={20} />,
      count: library.filter(game => game.favorite).length,
      color: "#ffc107",
    },
    {
      id: "installed",
      name: t("installed"),
      icon: <DownloadIcon size={20} />,
      count: library.filter(game => Boolean(game.executablePath)).length,
      color: "#1c9749",
    },
    {
      id: "not-played",
      name: t("not_played"),
      icon: <PlayIcon size={20} />,
      count: library.filter(game => !game.playTimeInMilliseconds || game.playTimeInMilliseconds === 0).length,
      color: "#801d1e",
    },
  ];

  const handleCollectionClick = useCallback((collectionId: string | null) => {
    onSelectCollection(collectionId);
  }, [onSelectCollection]);

  const handleEditClick = useCallback((e: React.MouseEvent, collection: GameCollection) => {
    e.stopPropagation();
    onEditCollection(collection);
  }, [onEditCollection]);

  const handleDeleteClick = useCallback((e: React.MouseEvent, collection: GameCollection) => {
    e.stopPropagation();
    onDeleteCollection(collection);
  }, [onDeleteCollection]);

  if (!isVisible) return null;

  return (
    <div className="library-collections-sidebar">
      <div className="library-collections-sidebar__overlay" onClick={onClose} />
      
      <div className="library-collections-sidebar__content">
        {/* Header */}
        <div className="library-collections-sidebar__header">
          <h3 className="library-collections-sidebar__title">{t("collections")}</h3>
          <button
            type="button"
            className="library-collections-sidebar__close"
            onClick={onClose}
            title={t("close")}
          >
            <XIcon size={16} />
          </button>
        </div>

        {/* All Games */}
        <div className="library-collections-sidebar__section">
          <button
            type="button"
            className={`library-collections-sidebar__item ${
              selectedCollection === null ? "library-collections-sidebar__item--active" : ""
            }`}
            onClick={() => handleCollectionClick(null)}
          >
            <div className="library-collections-sidebar__item-icon library-collections-sidebar__item-icon--all">
              <AppsIcon size={20} />
            </div>
            <div className="library-collections-sidebar__item-content">
              <span className="library-collections-sidebar__item-name">{t("all_games")}</span>
              <span className="library-collections-sidebar__item-count">{library.length}</span>
            </div>
          </button>
        </div>

        {/* Smart Collections */}
        <div className="library-collections-sidebar__section">
          <h4 className="library-collections-sidebar__section-title">{t("smart_collections")}</h4>
          {smartCollections.map((collection) => (
            <button
              key={collection.id}
              type="button"
              className={`library-collections-sidebar__item ${
                selectedCollection === collection.id ? "library-collections-sidebar__item--active" : ""
              }`}
              onClick={() => handleCollectionClick(collection.id)}
            >
              <div 
                className="library-collections-sidebar__item-icon"
                style={{ backgroundColor: collection.color }}
              >
                {collection.icon}
              </div>
              <div className="library-collections-sidebar__item-content">
                <span className="library-collections-sidebar__item-name">{collection.name}</span>
                <span className="library-collections-sidebar__item-count">{collection.count}</span>
              </div>
            </button>
          ))}
        </div>

        {/* Custom Collections */}
        <div className="library-collections-sidebar__section">
          <div className="library-collections-sidebar__section-header">
            <h4 className="library-collections-sidebar__section-title">{t("my_collections")}</h4>
            <Button
              onClick={onCreateCollection}
              theme="outline"
              size="small"
              className="library-collections-sidebar__create-button"
            >
              <PlusIcon size={14} />
            </Button>
          </div>

          {collections.length === 0 ? (
            <div className="library-collections-sidebar__empty">
              <p className="library-collections-sidebar__empty-text">
                {t("no_collections_yet")}
              </p>
              <Button
                onClick={onCreateCollection}
                theme="primary"
                size="small"
                className="library-collections-sidebar__empty-button"
              >
                <PlusIcon size={14} />
                {t("create_first_collection")}
              </Button>
            </div>
          ) : (
            collections.map((collection) => (
              <div
                key={collection.id}
                className={`library-collections-sidebar__item-container ${
                  selectedCollection === collection.id ? "library-collections-sidebar__item-container--active" : ""
                }`}
                onMouseEnter={() => setHoveredCollection(collection.id)}
                onMouseLeave={() => setHoveredCollection(null)}
              >
                <button
                  type="button"
                  className="library-collections-sidebar__item"
                  onClick={() => handleCollectionClick(collection.id)}
                >
                  <div
                    className="library-collections-sidebar__item-icon"
                    style={{ backgroundColor: collection.color }}
                  >
                    <FileDirectoryIcon size={20} />
                  </div>
                  <div className="library-collections-sidebar__item-content">
                    <span className="library-collections-sidebar__item-name">{collection.name}</span>
                    <span className="library-collections-sidebar__item-count">
                      {getCollectionGameCount(collection.id)}
                    </span>
                  </div>
                </button>

                {/* Collection Actions */}
                {hoveredCollection === collection.id && (
                  <div className="library-collections-sidebar__item-actions">
                    <button
                      type="button"
                      className="library-collections-sidebar__action-button"
                      onClick={(e) => handleEditClick(e, collection)}
                      title={t("edit_collection")}
                    >
                      <PencilIcon size={12} />
                    </button>
                    <button
                      type="button"
                      className="library-collections-sidebar__action-button library-collections-sidebar__action-button--danger"
                      onClick={(e) => handleDeleteClick(e, collection)}
                      title={t("delete_collection")}
                    >
                      <TrashIcon size={12} />
                    </button>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
