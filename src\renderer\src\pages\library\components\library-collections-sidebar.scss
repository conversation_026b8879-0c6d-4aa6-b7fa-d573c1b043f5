@use "../../../scss/globals.scss";

.library-collections-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  animation: sidebarSlideIn 0.3s ease-out;

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    cursor: pointer;
  }

  &__content {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 360px; // Wider for better touch targets
    background: linear-gradient(135deg, rgba(28, 28, 28, 0.95) 0%, rgba(21, 21, 21, 0.95) 100%);
    border-right: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(24px);
    box-shadow:
      0 16px 64px rgba(0, 0, 0, 0.4),
      0 8px 32px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow-y: auto;
    overflow-x: hidden;
    animation: sidebarContentSlideIn 0.3s ease-out;

    // Steam Deck optimizations (1280x800)
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 400px; // Even wider for Steam Deck
    }

    @media (max-width: 768px) {
      width: 320px;
    }

    @media (max-width: 480px) {
      width: 100%;
      max-width: 360px;
    }
  }

  // Header
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    position: sticky;
    top: 0;
    background: inherit;
    backdrop-filter: inherit;
    z-index: 1;
  }

  &__title {
    margin: 0;
    color: globals.$muted-color;
    font-size: 20px;
    font-weight: 700;
  }

  &__close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: globals.$muted-color;
      border-color: rgba(255, 255, 255, 0.2);
    }
  }

  // Sections
  &__section {
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    &:last-child {
      border-bottom: none;
      padding-bottom: calc(globals.$spacing-unit * 4);
    }
  }

  &__section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: calc(globals.$spacing-unit * 1.5);
  }

  &__section-title {
    margin: 0 0 calc(globals.$spacing-unit * 1.5) 0;
    color: globals.$body-color;
    font-size: globals.$small-font-size;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__create-button {
    min-width: auto;
    width: 28px;
    height: 28px;
    padding: 0;
  }

  // Collection Items
  &__item-container {
    position: relative;
    margin-bottom: calc(globals.$spacing-unit * 0.5);

    &--active {
      .library-collections-sidebar__item {
        background: rgba(22, 177, 149, 0.15);
        border-color: rgba(22, 177, 149, 0.3);
        color: globals.$muted-color;

        .library-collections-sidebar__item-icon {
          background: globals.$brand-teal;
          color: white;
        }
      }
    }
  }

  &__item {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    width: 100%;
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 2.5);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    min-height: 64px; // Touch target minimum

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      transform: translateX(3px);
    }

    &:active {
      transform: translateX(1px) scale(0.99);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$muted-color;

      .library-collections-sidebar__item-icon {
        background: globals.$brand-teal;
        color: white;

        &--all {
          background: globals.$brand-teal;
        }
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 72px;
      padding: calc(globals.$spacing-unit * 2.5) calc(globals.$spacing-unit * 3);
      gap: calc(globals.$spacing-unit * 2.5);
    }

    @media (max-width: 768px) {
      min-height: 60px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2);
    }
  }

  &__item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 14px;
    color: globals.$muted-color;
    flex-shrink: 0;
    transition: all 0.2s ease;

    &--all {
      background: rgba(22, 177, 149, 0.2);
      color: globals.$brand-teal;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 56px;
      height: 56px;
      border-radius: 16px;
    }

    @media (max-width: 768px) {
      width: 44px;
      height: 44px;
      border-radius: 12px;
    }
  }

  &__item-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 0.25);
  }

  &__item-name {
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 18px;
    }

    @media (max-width: 768px) {
      font-size: 15px;
    }
  }

  &__item-count {
    font-size: 14px;
    color: globals.$body-color;
    opacity: 0.8;
    font-weight: 500;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 16px;
    }

    @media (max-width: 768px) {
      font-size: 13px;
    }
  }

  // Collection Actions
  &__item-actions {
    position: absolute;
    top: 50%;
    right: calc(globals.$spacing-unit * 1.5);
    transform: translateY(-50%);
    display: flex;
    gap: calc(globals.$spacing-unit * 0.5);
    background: rgba(28, 28, 28, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: calc(globals.$spacing-unit * 0.25);
    backdrop-filter: blur(8px);
    animation: actionsSlideIn 0.2s ease-out;
  }

  &__action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: transparent;
    border: none;
    border-radius: 4px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: globals.$muted-color;
    }

    &--danger:hover {
      background: rgba(128, 29, 30, 0.2);
      color: globals.$danger-color;
    }
  }

  // Empty State
  &__empty {
    text-align: center;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 2);
  }

  &__empty-text {
    margin: 0 0 calc(globals.$spacing-unit * 2) 0;
    color: globals.$body-color;
    font-size: globals.$small-font-size;
    opacity: 0.8;
  }

  &__empty-button {
    min-width: auto;
  }
}

// Animations
@keyframes sidebarSlideIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes sidebarContentSlideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes actionsSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}
