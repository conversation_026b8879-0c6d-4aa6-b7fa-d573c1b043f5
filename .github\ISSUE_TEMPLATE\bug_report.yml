name: Bug Report
description: Create a report to help us improve. Write in English.
title: "[BUG] Write a title for your bug"
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for creating a bug report to help us improve!
  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: textarea
    id: bug-reproduce
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior. For example, "1. Go to '...', 2. Click on '...', 3. See error"
    validations:
      required: true
  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
    validations:
      required: false
  - type: textarea
    id: additional-info
    attributes:
      label: Additional information and data
      description: |
        Add screenshots and upload your all logs file here.
        Logs location on Windows: "%appdata%/hydralauncher/logs"
        Logs location on Linux: "~/.config/hydralauncher/logs"
    validations:
      required: true
  - type: input
    id: OS
    attributes:
      label: Operating System
      description: Which operating system are you using (e.g., Windows 11/Linux Distro/Steam Deck)?
    validations:
      required: true
  - type: input
    id: hydra-version
    attributes:
      label: Hydra Version
      description: Please provide the version of Hydra you are using.
    validations:
      required: true
  - type: checkboxes
    id: terms
    attributes:
      label: Before opening this Issue
      options:
        - label: I have searched the issues of this repository and believe that this is not a duplicate.
          required: true
        - label: I am aware that Hydra team does not offer any support or help regarding the downloaded games.
          required: true
        - label: I have read the [Frequently Asked Questions (FAQ)](https://github.com/hydralauncher/hydra/wiki/FAQ).
          required: true
