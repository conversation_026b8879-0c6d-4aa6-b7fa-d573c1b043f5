import type { LibraryGame } from "@types";

/**
 * Game Utility Functions
 * 
 * Centralized utility functions for game-related operations.
 * These functions provide consistent game state checking and formatting
 * across the application.
 */

/**
 * Checks if a game is playable (has an executable path)
 * @param game - The library game to check
 * @returns true if the game can be played, false otherwise
 */
export function isGamePlayable(game: LibraryGame): boolean {
  return Boolean(game.executablePath);
}

/**
 * Checks if a game is currently installed
 * @param game - The library game to check
 * @returns true if the game is installed, false otherwise
 */
export function isGameInstalled(game: LibraryGame): boolean {
  return Boolean(game.executablePath);
}

/**
 * Checks if a game is currently downloading
 * @param game - The library game to check
 * @returns true if the game is downloading, false otherwise
 */
export function isGameDownloading(game: LibraryGame): boolean {
  return game.download?.status === "active";
}

/**
 * Checks if a game download is paused
 * @param game - The library game to check
 * @returns true if the game download is paused, false otherwise
 */
export function isGameDownloadPaused(game: LibraryGame): boolean {
  return game.download?.status === "paused";
}

/**
 * Checks if a game has any download in progress (active or paused)
 * @param game - The library game to check
 * @returns true if the game has a download in progress, false otherwise
 */
export function hasGameDownloadInProgress(game: LibraryGame): boolean {
  return ["active", "paused"].includes(game.download?.status as string) &&
         game.download?.progress !== 1;
}

/**
 * Formats playtime from milliseconds to a human-readable string
 * @param playTimeInMilliseconds - Playtime in milliseconds
 * @param numberFormatter - Intl.NumberFormat instance for formatting
 * @returns Formatted playtime string (e.g., "2h 30m", "45m")
 */
export function formatPlayTime(
  playTimeInMilliseconds: number,
  numberFormatter: Intl.NumberFormat
): string {
  if (!playTimeInMilliseconds) return "0m";

  const hours = Math.floor(playTimeInMilliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((playTimeInMilliseconds % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0) {
    return `${numberFormatter.format(hours)}h ${minutes}m`;
  }
  return `${minutes}m`;
}

/**
 * Formats playtime for compact display (hours only if > 1 hour)
 * @param playTimeInMilliseconds - Playtime in milliseconds
 * @param numberFormatter - Intl.NumberFormat instance for formatting
 * @returns Compact formatted playtime string
 */
export function formatPlayTimeCompact(
  playTimeInMilliseconds: number,
  numberFormatter: Intl.NumberFormat
): string {
  const hours = playTimeInMilliseconds / (1000 * 60 * 60);
  if (hours < 1) {
    const minutes = Math.floor(playTimeInMilliseconds / (1000 * 60));
    return `${minutes}m`;
  }
  return `${numberFormatter.format(hours)}h`;
}

/**
 * Gets the primary action text for a game (Play or Download)
 * @param game - The library game
 * @returns Action text string
 */
export function getGamePrimaryActionText(game: LibraryGame): "play" | "download" {
  return isGamePlayable(game) ? "play" : "download";
}

/**
 * Gets the game status text for display
 * @param game - The library game
 * @returns Status text string
 */
export function getGameStatusText(game: LibraryGame): "installed" | "not_installed" | "downloading" | "paused" {
  if (isGameDownloading(game)) return "downloading";
  if (isGameDownloadPaused(game)) return "paused";
  if (isGameInstalled(game)) return "installed";
  return "not_installed";
}

/**
 * Checks if a game is marked as favorite
 * @param game - The library game to check
 * @returns true if the game is favorite, false otherwise
 */
export function isGameFavorite(game: LibraryGame): boolean {
  return Boolean(game.favorite);
}

/**
 * Gets the last played date as a formatted string
 * @param game - The library game
 * @returns Formatted date string or null if never played
 */
export function getLastPlayedDate(game: LibraryGame): string | null {
  if (!game.lastTimePlayed) return null;
  return new Date(game.lastTimePlayed).toLocaleDateString();
}

/**
 * Checks if a game has been played before
 * @param game - The library game to check
 * @returns true if the game has been played, false otherwise
 */
export function hasGameBeenPlayed(game: LibraryGame): boolean {
  return Boolean(game.lastTimePlayed);
}

/**
 * Gets download progress as a percentage
 * @param game - The library game
 * @returns Download progress as a number between 0 and 100, or null if no download
 */
export function getDownloadProgress(game: LibraryGame): number | null {
  if (!game.download) return null;
  return Math.round((game.download.progress || 0) * 100);
}

/**
 * Formats download progress for display
 * @param progress - Progress as a decimal (0-1)
 * @returns Formatted progress string (e.g., "75%")
 */
export function formatDownloadProgress(progress?: number): string {
  if (progress === undefined || progress === null) return "0%";
  return `${Math.round(progress * 100)}%`;
}

/**
 * Checks if a game can be launched (is playable and not currently downloading)
 * @param game - The library game to check
 * @returns true if the game can be launched, false otherwise
 */
export function canLaunchGame(game: LibraryGame): boolean {
  return isGamePlayable(game) && !isGameDownloading(game);
}

/**
 * Gets the appropriate icon name for a game's current state
 * @param game - The library game
 * @returns Icon name string
 */
export function getGameStateIcon(game: LibraryGame): "play" | "download" | "pause" | "check" {
  if (isGameDownloading(game)) return "pause";
  if (isGameDownloadPaused(game)) return "download";
  if (isGamePlayable(game)) return "play";
  return "download";
}
