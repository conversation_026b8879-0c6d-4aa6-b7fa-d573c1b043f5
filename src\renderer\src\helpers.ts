import type { GameShop } from "@types";

import Color from "color";
import { THEME_WEB_STORE_URL } from "./constants";

export const formatDownloadProgress = (
  progress?: number,
  fractionDigits?: number
) => {
  if (!progress) return "0%";
  const progressPercentage = progress * 100;

  if (Number(progressPercentage.toFixed(fractionDigits ?? 2)) % 1 === 0)
    return `${Math.floor(progressPercentage)}%`;

  return `${progressPercentage.toFixed(fractionDigits ?? 2)}%`;
};

export const getSteamLanguage = (language: string) => {
  // Complete mapping for all supported languages
  const languageMap: { [key: string]: string } = {
    // English
    'en': 'english',

    // European languages
    'es': 'spanish',           // Español
    'fr': 'french',            // Français
    'de': 'german',            // Deutsch
    'it': 'italian',           // Italiano
    'pt': 'portuguese',        // Português (Portugal)
    'pt-PT': 'portuguese',     // Português (Portugal)
    'pt-BR': 'brazilian',      // Português (Brasil)
    'nl': 'dutch',             // Nederlands
    'da': 'danish',            // Dansk
    'sv': 'swedish',           // Svenska
    'nb': 'norwegian',         // Norsk Bokmål
    'pl': 'polish',            // Polski
    'cs': 'czech',             // Čeština
    'hu': 'hungarian',         // Magyar
    'ro': 'romanian',          // Română
    'tr': 'turkish',           // Türkçe
    'et': 'estonian',          // Eesti
    'ca': 'catalan',           // Català

    // Slavic languages
    'ru': 'russian',           // Русский
    'uk': 'ukrainian',         // Українська
    'be': 'russian',           // беларуская мова (fallback to Russian)
    'bg': 'bulgarian',         // Български

    // Asian languages
    'zh': 'schinese',          // 中文 (Simplified Chinese)
    'ko': 'koreana',           // 한국어
    'id': 'indonesian',        // Bahasa Indonesia

    // Middle Eastern languages
    'ar': 'arabic',            // العربية
    'fa': 'persian',           // فارسی

    // Central Asian languages
    'kk': 'kazakh',            // қазақ тілі
    'uz': 'uzbek',             // Uzbek
  };

  // Handle language codes with region (e.g., pt-BR, pt-PT)
  const normalizedLang = language.toLowerCase();

  // First try exact match
  if (languageMap[normalizedLang]) {
    return languageMap[normalizedLang];
  }

  // Then try base language (e.g., 'pt' from 'pt-BR')
  const baseLang = normalizedLang.split('-')[0];
  if (languageMap[baseLang]) {
    return languageMap[baseLang];
  }

  // Default to English
  return 'english';
};

export const buildGameDetailsPath = (
  game: { shop: GameShop; objectId: string; title: string },
  params: Record<string, string> = {}
) => {
  const searchParams = new URLSearchParams({ title: game.title, ...params });
  return `/game/${game.shop}/${game.objectId}?${searchParams.toString()}`;
};

export const buildGameAchievementPath = (
  game: { shop: GameShop; objectId: string; title: string },
  user?: { userId: string }
) => {
  const searchParams = new URLSearchParams({
    title: game.title,
    shop: game.shop,
    objectId: game.objectId,
    userId: user?.userId || "",
  });

  return `/achievements/?${searchParams.toString()}`;
};

export const darkenColor = (color: string, amount: number, alpha: number = 1) =>
  new Color(color).darken(amount).alpha(alpha).toString();

export const injectCustomCss = (
  css: string,
  target: HTMLElement = document.head
) => {
  try {
    target.querySelector("#custom-css")?.remove();

    if (css.startsWith(THEME_WEB_STORE_URL)) {
      const link = document.createElement("link");
      link.id = "custom-css";
      link.rel = "stylesheet";
      link.href = css;
      target.appendChild(link);
    } else {
      const style = document.createElement("style");
      style.id = "custom-css";
      style.textContent = `
        ${css}
      `;
      target.appendChild(style);
    }
  } catch (error) {
    console.error("failed to inject custom css:", error);
  }
};

export const removeCustomCss = (target: HTMLElement = document.head) => {
  target.querySelector("#custom-css")?.remove();
};
