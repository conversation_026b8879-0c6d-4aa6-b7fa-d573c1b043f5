@use "../../../scss/globals.scss";

.collections-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: calc(globals.$spacing-unit * 2);
  animation: sidebarSlideIn 0.5s ease-out;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: calc(globals.$spacing-unit * 2);
    padding-bottom: calc(globals.$spacing-unit * 1.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  &__title {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: globals.$muted-color;
    background: linear-gradient(135deg, globals.$muted-color 0%, rgba(255, 255, 255, 0.8) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.01em;
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 2);
  }

  &__separator {
    margin: calc(globals.$spacing-unit * 1.5) 0 globals.$spacing-unit 0;
    padding-top: calc(globals.$spacing-unit * 1.5);
    border-top: 1px solid globals.$border-color;
    
    span {
      font-size: globals.$small-font-size;
      color: globals.$body-color;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }
  }

  &__item-container {
    position: relative;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
      border-color: rgba(255, 255, 255, 0.1);
      transform: translateX(4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .collections-list__item-actions {
        opacity: 1;
      }
    }

    &--active {
      background: linear-gradient(135deg, #3e62c0 0%, #2d4a8f 100%);
      border-color: rgba(62, 98, 192, 0.3);
      box-shadow: 0 4px 16px rgba(62, 98, 192, 0.2);
      transform: translateX(2px);

      .collections-list__item {
        color: white;
      }

      .collections-list__item-count {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  &__item {
    width: 100%;
    background: transparent;
    border: none;
    padding: calc(globals.$spacing-unit * 2);
    cursor: pointer;
    text-align: left;
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    border-radius: 12px;
    transition: all 0.3s ease;
    color: globals.$muted-color;
    font-weight: 500;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 0;
      background: linear-gradient(135deg, #3e62c0 0%, #2d4a8f 100%);
      border-radius: 2px;
      transition: height 0.3s ease;
    }

    &:hover::before {
      height: 60%;
    }
  }

  &__item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  &__item-color-main {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;

    svg {
      color: rgba(255, 255, 255, 0.9);
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }
  }

  &__item-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 0;
  }

  &__item-name {
    font-size: globals.$body-font-size;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__item-count {
    font-size: globals.$small-font-size;
    background-color: rgba(255, 255, 255, 0.1);
    color: globals.$body-color;
    padding: calc(globals.$spacing-unit / 4) calc(globals.$spacing-unit / 2);
    border-radius: 8px;
    font-weight: 500;
    flex-shrink: 0;
    margin-left: globals.$spacing-unit;
  }

  &__item-actions {
    position: absolute;
    right: calc(globals.$spacing-unit * 1.5);
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: calc(globals.$spacing-unit / 2);
    opacity: 0;
    transition: opacity ease 0.2s;
    background-color: globals.$dark-background-color;
    padding: calc(globals.$spacing-unit / 2);
    border-radius: 4px;
    border: 1px solid globals.$border-color;
  }

  &__action-button {
    background: transparent;
    border: none;
    padding: calc(globals.$spacing-unit / 2);
    cursor: pointer;
    color: globals.$body-color;
    border-radius: 3px;
    transition: all ease 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: globals.$muted-color;
    }

    &--danger:hover {
      background-color: globals.$danger-color;
      color: white;
    }
  }

  &__empty {
    text-align: center;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 2);
    color: globals.$body-color;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: globals.$spacing-unit;

    svg {
      opacity: 0.5;
    }

    p {
      margin: 0;
      font-size: globals.$body-font-size;
    }
  }

  &__empty-hint {
    font-size: globals.$small-font-size !important;
    opacity: 0.7;
  }
}

// Enhanced animations
@keyframes sidebarSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Stagger animation for collection items
.collections-list__item-container {
  animation: itemSlideIn 0.4s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.05s * $i};
    }
  }
}

@keyframes itemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-15px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Loading state
.collections-list.loading {
  .collections-list__item-container {
    pointer-events: none;
    opacity: 0.6;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 1.5s infinite;
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
