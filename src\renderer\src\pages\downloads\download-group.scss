@use "../../scss/globals.scss";

.download-group {
  display: flex;
  flex-direction: column;
  gap: calc(globals.$spacing-unit * 2);

  &__details-with-article {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit / 2);
    align-self: flex-start;
    cursor: pointer;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(globals.$spacing-unit * 2);

    &-divider {
      flex: 1;
      background-color: globals.$border-color;
      height: 1px;
    }

    &-count {
      font-weight: 400;
    }
  }

  &__title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: globals.$spacing-unit;
    gap: globals.$spacing-unit;
  }

  &__title {
    font-weight: bold;
    cursor: pointer;
    color: globals.$body-color;
    text-align: left;
    font-size: 16px;
    display: block;

    &:hover {
      text-decoration: underline;
    }
  }

  &__downloads {
    width: 100%;
    gap: calc(globals.$spacing-unit * 2);
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    margin-top: globals.$spacing-unit;
  }

  &__item {
    width: 100%;
    background-color: globals.$background-color;
    display: flex;
    border-radius: 8px;
    border: solid 1px globals.$border-color;
    overflow: hidden;
    box-shadow: 0px 0px 5px 0px #000000;
    transition: all ease 0.2s;
    height: 140px;
    min-height: 140px;
    max-height: 140px;
    position: relative;

    &--hydra {
      box-shadow: 0px 0px 16px 0px rgba(12, 241, 202, 0.15);
    }
  }
  &__cover {
    width: 280px;
    min-width: 280px;
    height: auto;
    border-right: solid 1px globals.$border-color;
    position: relative;
    z-index: 1;

    &-content {
      width: 100%;
      height: 100%;
      padding: globals.$spacing-unit;
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
    }

    &-backdrop {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        0deg,
        rgba(0, 0, 0, 0.8) 5%,
        transparent 100%
      );
      display: flex;
      overflow: hidden;
      z-index: 1;
    }

    &-image {
      width: 100%;
      height: 100%;
      position: absolute;
      z-index: -1;
    }
  }

  &__right-content {
    display: flex;
    padding: calc(globals.$spacing-unit * 2);
    flex: 1;
    gap: globals.$spacing-unit;
    background: linear-gradient(90deg, transparent 20%, rgb(0 0 0 / 20%) 100%);
  }

  &__details {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: center;
    gap: calc(globals.$spacing-unit / 2);
    font-size: 14px;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: globals.$spacing-unit;
  }

  &__menu-button {
    position: absolute;
    top: 12px;
    right: 12px;
    border-radius: 50%;
    border: none;
    padding: 8px;
    min-height: unset;
  }

  &__hydra-gradient {
    background: linear-gradient(90deg, #01483c 0%, #0cf1ca 50%, #01483c 100%);
    box-shadow: 0px 0px 8px 0px rgba(12, 241, 202, 0.15);
    width: 100%;
    position: absolute;
    bottom: 0;
    height: 2px;
    z-index: 1;
  }
}
