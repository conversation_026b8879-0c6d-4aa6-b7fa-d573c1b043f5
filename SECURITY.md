# Security Policy

## Purpose of the Policy

The purpose of this Security Policy is to ensure the security of our project and maintain the trust of the community.

## Who is Affected by the Policy

This policy applies to all members of our project community, including developers, testers, repository administrators, and users.

## Supported Versions

Use this section to tell people about which versions of your project are
currently being supported with security updates.

| Version | Supported          |
| ------- | ------------------ |
| 2.0.x   | :white_check_mark: |
| < 1.2.0 | :x:                |

## Development Recommendations

### Best Practices

- Follow secure coding principles.
- Use well-established libraries and frameworks.
- Regularly update dependencies.
- Conduct thorough testing, including security-related tests.

### Unrecommended Practices

- Do not use known vulnerabilities that have not been patched.
- Do not publish sensitive information such as API keys or passwords.
- Do not vote for changes that degrade the security of the project.

### User-Generated Content

- Ensure that user-generated content does not contain hidden threats.
- Be cautious when handling user data.

### Community Interaction

- Treat each other with respect and politeness.
- Do not spread spam or spam bots.
- Follow community guidelines.

### Vulnerability Discovery and Reporting

- If you discover a vulnerability, report it as an issue on GitHub.
- Your report should contain detailed information about the vulnerability, including steps to resolve it.

### Reporting Method

To report a vulnerability, create a new issue on GitHub and use branch isolation to provide details about the vulnerability.

### Details to Provide

Please provide the following information about the vulnerability:

- Description of the vulnerability
- Steps to resolve the vulnerability
- Versions on which the vulnerability was found
- Code examples illustrating the vulnerability (if it is safe to do so)

### Expected Behavior

- If we accept the reported vulnerability, we will release a patch and update the security information on GitHub.
- If we reject the reported vulnerability, we will provide an explanation.
