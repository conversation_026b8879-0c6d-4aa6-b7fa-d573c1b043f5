@use "../../../scss/globals.scss";

/**
 * LibraryFilters Styles
 *
 * Touch-optimized filters panel for library games.
 * Provides comprehensive filtering options with progressive disclosure.
 *
 * Key Features:
 * - Touch-friendly filter controls (48px+ minimum)
 * - Steam Deck optimizations (52px+ targets)
 * - Progressive disclosure for complex filters
 * - Responsive layout for different screen sizes
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 */

.library-filters {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  margin-bottom: 0;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: calc(globals.$spacing-unit * 3);
    padding-bottom: calc(globals.$spacing-unit * 2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      margin-bottom: calc(globals.$spacing-unit * 3.5);
      padding-bottom: calc(globals.$spacing-unit * 2.5);
    }

    @media (max-width: 768px) {
      margin-bottom: calc(globals.$spacing-unit * 2.5);
      padding-bottom: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__title {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 20px;
    }

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 4);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      gap: calc(globals.$spacing-unit * 4.5);
    }

    @media (max-width: 768px) {
      gap: calc(globals.$spacing-unit * 3);
    }
  }

  // Filter Section - Touch Optimized
  &__section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: calc(globals.$spacing-unit * 3);
    backdrop-filter: blur(16px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);

    &:hover {
      border-color: rgba(255, 255, 255, 0.15);
      box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    }

    &--active {
      background: linear-gradient(135deg, rgba(22, 177, 149, 0.08) 0%, rgba(22, 177, 149, 0.03) 100%);
      border: 1px solid rgba(22, 177, 149, 0.2);
      box-shadow:
        0 6px 24px rgba(22, 177, 149, 0.15),
        inset 0 1px 0 rgba(22, 177, 149, 0.1);

      .library-filters__section-title {
        color: globals.$brand-teal;
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      padding: calc(globals.$spacing-unit * 3.5);
      border-radius: 24px;
    }

    @media (max-width: 768px) {
      padding: calc(globals.$spacing-unit * 2.5);
      border-radius: 18px;
    }
  }

  // Section Title - Touch Optimized
  &__section-title {
    margin: 0 0 calc(globals.$spacing-unit * 3) 0;
    font-size: 16px;
    font-weight: 700;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.75px;
    position: relative;
    padding-bottom: calc(globals.$spacing-unit * 1.5);
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    min-height: 48px; // Touch target minimum

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 60px;
      height: 3px;
      background: linear-gradient(90deg, globals.$brand-teal, rgba(22, 177, 149, 0.3));
      border-radius: 2px;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 18px;
      min-height: 52px;
      gap: calc(globals.$spacing-unit * 2.5);
    }

    @media (max-width: 768px) {
      font-size: 15px;
      min-height: 44px;
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  // Section Icon - Touch Optimized
  &__section-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.08);
    color: globals.$muted-color;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    svg {
      width: 18px;
      height: 18px;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 36px;
      height: 36px;
      border-radius: 12px;

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  &__section-text {
    flex: 1;
    font-size: 16px;
    font-weight: 700;
    color: globals.$muted-color;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 18px;
    }

    @media (max-width: 768px) {
      font-size: 15px;
    }
  }

  &__active-count {
    font-size: 13px;
    color: white;
    background: globals.$brand-teal;
    padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 1);
    border-radius: 16px;
    font-weight: 700;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    box-shadow: 0 2px 8px rgba(22, 177, 149, 0.3);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 14px;
      min-width: 32px;
      height: 32px;
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1.25);
    }
  }

  &__options {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1.5);
  }

  &__checkbox-label {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    color: globals.$muted-color;
    font-weight: 500;
  }

  &__checkbox-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.06);
    color: globals.$body-color;
    transition: all 0.2s ease;

    svg {
      width: 14px;
      height: 14px;
    }
  }

  &__checkbox-text {
    flex: 1;
    font-size: 14px;
    color: globals.$muted-color;
  }

  &__count {
    font-size: globals.$small-font-size;
    color: white;
    background-color: globals.$success-color;
    padding: calc(globals.$spacing-unit / 4) calc(globals.$spacing-unit / 2);
    border-radius: 8px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(28, 151, 73, 0.3);
  }

  &__genre-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: calc(globals.$spacing-unit * 1.5);
    padding: calc(globals.$spacing-unit * 1);

    @media (max-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    @media (max-width: 1024px) {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: calc(globals.$spacing-unit * 1);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr 1fr;
      gap: calc(globals.$spacing-unit * 0.75);
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }

    // Enhanced CheckboxField styles for better visual appeal
    .checkbox-field {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.02) 100%);
      border: 1px solid rgba(255, 255, 255, 0.08);
      border-radius: 12px;
      padding: calc(globals.$spacing-unit * 1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
        border-color: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &:has(input:checked) {
        background: linear-gradient(135deg, rgba(28, 151, 73, 0.15) 0%, rgba(28, 151, 73, 0.08) 100%);
        border-color: rgba(28, 151, 73, 0.4);
        box-shadow: 0 4px 16px rgba(28, 151, 73, 0.2);

        .library-filters__genre-count {
          background: linear-gradient(135deg, globals.$success-color 0%, rgba(28, 151, 73, 0.9) 100%);
          color: white;
          border-color: globals.$success-color;
          box-shadow: 0 2px 8px rgba(28, 151, 73, 0.3);
        }
      }

      &__label {
        text-overflow: unset;
        overflow: visible;
        white-space: normal;
        width: 100%;
        min-width: 0;
        cursor: pointer;
      }

      &__checkbox {
        width: 18px;
        height: 18px;
        min-width: 18px;
        min-height: 18px;
      }
    }
  }

  &__checkbox-label {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.25);
    color: globals.$muted-color;

    svg {
      color: globals.$body-color;
      opacity: 0.8;
      transition: all 0.2s ease;
    }

    &:hover svg {
      opacity: 1;
      color: globals.$muted-color;
    }
  }

  // Enhanced hover states for better UX
  &__genre-list .checkbox-field {
    &:hover {
      .library-filters__genre-info {
        svg {
          opacity: 1;
          color: globals.$muted-color;
          transform: scale(1.05);
        }

        span {
          color: #ffffff;
        }
      }
    }

    // Focus states for accessibility
    &:focus-within {
      outline: 2px solid rgba(28, 151, 73, 0.4);
      outline-offset: 2px;
    }

    // Disabled state
    &:has(input:disabled) {
      opacity: 0.4;
      cursor: not-allowed;

      &:hover {
        background: rgba(255, 255, 255, 0.02);
        border-color: rgba(255, 255, 255, 0.06);
      }
    }
  }

  &__genre-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    color: globals.$muted-color;
    gap: calc(globals.$spacing-unit * 1.5);
    padding: 0;
    min-height: 36px;
    font-size: globals.$body-font-size;
    font-weight: 500;
  }

  &__genre-info {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.25);
    flex: 1;
    min-width: 0;

    svg {
      color: globals.$muted-color;
      opacity: 0.9;
      transition: all 0.2s ease;
      flex-shrink: 0;
      width: 16px;
      height: 16px;
    }

    span {
      flex: 1;
      min-width: 0;
      font-size: globals.$body-font-size;
      font-weight: 500;
      color: globals.$muted-color;
      line-height: 1.4;
      letter-spacing: 0.01em;
    }
  }

  &__genre-count {
    font-size: 12px;
    color: globals.$body-color;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: calc(globals.$spacing-unit * 0.375) calc(globals.$spacing-unit * 0.75);
    border-radius: 16px;
    font-weight: 600;
    min-width: 28px;
    height: 22px;
    text-align: center;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    // Better number formatting for larger counts
    &[data-count="0"] {
      opacity: 0.5;
    }

    // Hover effect
    .checkbox-field:hover & {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.25);
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }
  }

  &__no-genres {
    margin: 0;
    padding: calc(globals.$spacing-unit * 3);
    text-align: center;
    color: globals.$body-color;
    font-size: globals.$small-font-size;
    opacity: 0.7;
    font-style: italic;
    background: rgba(255, 255, 255, 0.02);
    border: 1px dashed rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-top: calc(globals.$spacing-unit * 1);
  }

  // Loading state
  &__loading {
    margin: 0;
    padding: calc(globals.$spacing-unit * 3);
    text-align: center;
    color: globals.$muted-color;
    font-size: globals.$body-font-size;
    opacity: 0.8;

    &::after {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-left: calc(globals.$spacing-unit * 0.5);
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-top: 2px solid globals.$muted-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  // Smooth transitions for genre items
  &__genre-list .checkbox-field {
    animation: fadeInUp 0.3s ease-out;
    animation-fill-mode: both;

    @for $i from 1 through 20 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.05}s;
      }
    }
  }
}

// Animation for filter panel
.library-filters {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// New genre chip styles
.library-filters {
  &__genre-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: calc(globals.$spacing-unit * 1.5);
    margin-top: calc(globals.$spacing-unit * 1);

    @media (max-width: 1024px) {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: calc(globals.$spacing-unit * 1);
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: calc(globals.$spacing-unit * 0.75);
    }
  }

  &__genre-chip {
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: calc(globals.$spacing-unit * 1.25);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

      &::before {
        opacity: 1;
      }

      .library-filters__genre-chip-icon {
        background: rgba(255, 255, 255, 0.12);
        transform: scale(1.1);
      }

      .library-filters__genre-chip-text {
        color: white;
      }
    }

    &--selected {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.25);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

      &::before {
        opacity: 0.7;
      }

      .library-filters__genre-chip-icon {
        background: white;
        color: globals.$background-color;
      }

      .library-filters__genre-chip-text {
        color: white;
        font-weight: 600;
      }

      .library-filters__genre-chip-count {
        background: white;
        color: globals.$background-color;
        font-weight: 700;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }
    }
  }

  &__genre-chip-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__genre-chip-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.08);
    color: globals.$muted-color;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;

    svg {
      width: 14px;
      height: 14px;
    }
  }

  &__genre-chip-text {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
    color: globals.$muted-color;
    transition: all 0.3s ease;
    line-height: 1.3;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__genre-chip-count {
    font-size: 11px;
    color: globals.$body-color;
    background: rgba(255, 255, 255, 0.1);
    padding: calc(globals.$spacing-unit * 0.25) calc(globals.$spacing-unit * 0.5);
    border-radius: 8px;
    font-weight: 600;
    min-width: 20px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 3);
    color: globals.$muted-color;
    font-size: 14px;

    &-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-top: 2px solid globals.$muted-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  &__no-genres {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 4);
    color: globals.$body-color;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px dashed rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    text-align: center;

    svg {
      opacity: 0.5;
    }

    span {
      opacity: 0.7;
      font-style: italic;
    }
  }
}
