@use "../../scss/globals.scss";

.sidebar-library {
  margin-bottom: calc(globals.$spacing-unit * 2);

  &__header {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit / 2);
    margin-bottom: calc(globals.$spacing-unit);
    padding-bottom: calc(globals.$spacing-unit / 2);
    border-bottom: 1px solid globals.$border-color;
  }

  &__title {
    font-size: 14px;
    font-weight: 600;
    color: globals.$muted-color;
    margin: 0;
  }

  &__links {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 4);
  }

  &__link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(globals.$spacing-unit / 2) calc(globals.$spacing-unit);
    background: transparent;
    border: none;
    border-radius: 6px;
    color: globals.$muted-color;
    text-align: left;
    cursor: pointer;
    transition: all 0.15s ease;
    width: 100%;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
      color: white;
    }
  }

  &__link-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit / 2);
    flex: 1;
  }

  &__link-label {
    font-size: 13px;
    font-weight: 500;
  }

  &__link-count {
    font-size: 12px;
    font-weight: 600;
    color: globals.$muted-color;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
  }
}
