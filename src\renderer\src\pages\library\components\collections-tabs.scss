@use "../../../scss/globals.scss";

.collections-tabs {
  width: 100%;
  margin-bottom: calc(globals.$spacing-unit * 3);
  animation: slideIn 0.4s ease-out;
  // Ensure enough space for hover animations
  padding: calc(globals.$spacing-unit * 0.5) 0;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: calc(globals.$spacing-unit * 2);

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: globals.$muted-color;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  &__container {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1) 0;
    background: transparent;
    border: none;
    border-radius: 0;
    overflow-x: auto;
    overflow-y: visible;
    // Add extra padding to prevent clipping during hover animations
    padding-top: calc(globals.$spacing-unit * 1.5);
    padding-bottom: calc(globals.$spacing-unit * 1.5);
    margin-top: calc(globals.$spacing-unit * -0.5);
    margin-bottom: calc(globals.$spacing-unit * -0.5);

    // Custom scrollbar for horizontal scroll
    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      transition: background 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  &__tab-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: visible;
    padding: calc(globals.$spacing-unit * 0.5);
    gap: calc(globals.$spacing-unit * 0.5);

    &:hover:not(&--active) {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

      .collections-tabs__tab {
        background: transparent;
        border: none;
        box-shadow: none;
        transform: none;

        .collections-tabs__tab-name {
          color: globals.$muted-color;
        }

        .collections-tabs__tab-count {
          background: rgba(255, 255, 255, 0.15);
          color: globals.$muted-color;
        }

        .collections-tabs__tab-color {
          border-color: rgba(255, 255, 255, 0.3);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

          svg {
            color: rgba(255, 255, 255, 0.95);
          }
        }
      }

      .collections-tabs__action-button {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: globals.$muted-color;
        transform: none;
      }
    }

    &--active {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
      border: 1px solid rgba(255, 255, 255, 0.4);
      box-shadow:
        0 6px 24px rgba(255, 255, 255, 0.25),
        0 2px 8px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);

      .collections-tabs__tab {
        background: transparent;
        border: none;
        box-shadow: none;
        transform: none;
        color: globals.$dark-background-color;

        .collections-tabs__tab-name {
          color: rgba(0, 0, 0, 0.9);
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .collections-tabs__tab-count {
          background: rgba(0, 0, 0, 0.12);
          color: rgba(0, 0, 0, 0.85);
          font-weight: 700;
          border: 1px solid rgba(0, 0, 0, 0.08);
          box-shadow:
            0 2px 4px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .collections-tabs__tab-color {
          border-color: rgba(0, 0, 0, 0.3);
          box-shadow:
            0 3px 16px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);

          &::before {
            opacity: 0.8;
          }

          svg {
            color: rgba(255, 255, 255, 1);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.6));
          }
        }
      }

      .collections-tabs__action-button {
        background: rgba(0, 0, 0, 0.12);
        border-color: rgba(0, 0, 0, 0.25);
        color: rgba(0, 0, 0, 0.8);
        box-shadow:
          0 2px 4px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);

        &:hover {
          background: rgba(0, 0, 0, 0.18);
          border-color: rgba(0, 0, 0, 0.3);
          color: rgba(0, 0, 0, 0.9);
          transform: none;
          box-shadow:
            0 3px 8px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  &__tab {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.25);
    padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.5);
    background: transparent;
    border: none;
    border-radius: 8px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: globals.$body-font-size;
    min-height: 40px;
    position: relative;
    flex: 1;
  }

  &__tab-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    svg {
      transition: color 0.2s ease;
    }
  }

  &__tab-color {
    width: 28px;
    height: 28px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 3px 12px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: calc(globals.$spacing-unit * 0.75);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);

    // Enhanced gradient overlay for better visual integration
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
      border-radius: 6px;
      transition: opacity 0.3s ease;
    }

    // Subtle inner glow effect
    &::after {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      right: 1px;
      bottom: 1px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      border-radius: 6px;
      pointer-events: none;
    }

    svg {
      color: rgba(255, 255, 255, 0.95);
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
      position: relative;
      z-index: 2;
      transition: all 0.3s ease;
    }

    // Enhanced styling for "All Games" tab with better contrast
    &--all-games {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
      border-color: rgba(255, 255, 255, 0.3);
      box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);

      &::before {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      }

      svg {
        color: rgba(255, 255, 255, 1);
        filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.7));
      }
    }
  }

  // Enhanced "All Games" tab styling when active
  &__tab-wrapper--active {
    .collections-tabs__tab-color--all-games {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.8) 100%);
      border-color: rgba(0, 0, 0, 0.7);
      box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);

      &::before {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
      }

      svg {
        color: rgba(255, 255, 255, 1);
        filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.8));
        transform: scale(1.05);
      }
    }
  }

  &__tab-name {
    font-weight: 500;
    transition: all 0.2s ease;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__tab-count {
    background: rgba(255, 255, 255, 0.1);
    color: globals.$body-color;
    padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 1);
    border-radius: 12px;
    font-size: globals.$small-font-size;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
    transition: all 0.2s ease;
  }

  &__tab-actions {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  &__action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 6px;
    color: globals.$muted-color;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 1;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.2);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: scale(0.95);
    }

    svg {
      transition: transform 0.2s ease;
    }

    &:hover svg {
      transform: scale(1.1);
    }
  }

  &__dropdown-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99998;
    background: transparent;
  }

  &__dropdown {
    position: fixed;
    background: linear-gradient(135deg, globals.$dark-background-color 0%, rgba(21, 21, 21, 0.98) 100%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 12px;
    padding: calc(globals.$spacing-unit * 1);
    backdrop-filter: blur(24px);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.6),
      0 4px 16px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 99999;
    min-width: 160px;
    animation: dropdownSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &::before {
      content: '';
      position: absolute;
      top: -7px;
      right: 16px;
      width: 14px;
      height: 14px;
      background: linear-gradient(135deg, globals.$dark-background-color 0%, rgba(21, 21, 21, 0.98) 100%);
      border: 1px solid rgba(255, 255, 255, 0.25);
      border-bottom: none;
      border-right: none;
      transform: rotate(45deg);
      z-index: -1;
    }
  }

  &__dropdown-item {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.25);
    width: 100%;
    padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.5);
    background: transparent;
    border: none;
    border-radius: 8px;
    color: globals.$muted-color;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: globals.$body-font-size;
    font-weight: 500;
    text-align: left;
    margin-bottom: calc(globals.$spacing-unit * 0.25);

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
      color: white;
      transform: translateX(2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    &--danger {
      &:hover {
        background: linear-gradient(135deg, rgba(128, 29, 30, 0.3) 0%, rgba(128, 29, 30, 0.2) 100%);
        color: #ff6b6b;
        box-shadow: 0 2px 8px rgba(128, 29, 30, 0.3);
      }
    }

    svg {
      flex-shrink: 0;
      transition: transform 0.2s ease;
    }

    &:hover svg {
      transform: scale(1.1);
    }
  }

  &__create-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 0.75);
    padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.5);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: globals.$muted-color;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    font-size: globals.$body-font-size;
    font-weight: 600;
    white-space: nowrap;

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.15) 100%);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    &:active {
      transform: translateY(-1px);
    }

    svg {
      flex-shrink: 0;
      transition: transform 0.3s ease;
    }

    &:hover svg {
      transform: rotate(90deg) scale(1.1);
    }
  }
}

// Animations
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .collections-tabs {
    &__tab-name {
      max-width: 80px;
    }

    &__create-button {
      span {
        display: none;
      }
    }
  }
}
