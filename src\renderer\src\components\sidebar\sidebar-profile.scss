@use "../../scss/globals.scss";

.sidebar-profile {
  position: relative;
  display: flex;
  align-items: center;
  gap: globals.$spacing-unit;
  padding: globals.$spacing-unit calc(globals.$spacing-unit * 2);

  &__button {
    display: flex;
    cursor: pointer;
    transition: all ease 0.1s;
    color: globals.$muted-color;
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
    padding: globals.$spacing-unit globals.$spacing-unit;
    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }

  &__button-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit + globals.$spacing-unit / 2);
    width: 100%;
  }

  &__button-information {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    min-width: 0;
  }

  &__button-title {
    font-weight: bold;
    font-size: globals.$body-font-size;
    width: 100%;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__friends-button {
    color: globals.$muted-color;
    cursor: pointer;
    border-radius: 50%;
    width: 40px;
    min-width: 40px;
    min-height: 40px;
    height: 40px;
    background-color: globals.$background-color;
    position: relative;
    transition: all ease 0.3s;
    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }

  &__friends-button-badge {
    background-color: globals.$success-color;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    position: absolute;
    top: -5px;
    right: -5px;
  }

  &__game-running-icon {
    border-radius: 4px;
  }

  &__button-game-running-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    text-align: left;
    line-height: 1.15;
  }
}
