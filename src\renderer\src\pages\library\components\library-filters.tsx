import { use<PERSON><PERSON><PERSON>, use<PERSON>emo, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  XIcon,
  ZapIcon,
  LocationIcon,
  StarIcon,
  GraphIcon,
  GearIcon,
  TrophyIcon,
  RocketIcon,
  CircleIcon,
  ArrowUpIcon,
  ShieldIcon,
  FlameIcon,
  MoonIcon,
  SquareIcon,
  HeartIcon,
  SmileyIcon,
  CheckIcon,
  DownloadIcon,
  TagIcon,
  FilterIcon
} from "@primer/octicons-react";

import { Button, CheckboxField } from "@renderer/components";
import { useAppDispatch, useAppSelector, useLibrary } from "@renderer/hooks";
import { setLibraryFilters } from "@renderer/features/library-collections-slice";
import { generateGameGenresMap, COMMON_GENRES, countGamesByGenre } from "@renderer/utils/genre-utils";

import "./library-filters.scss";

/**
 * LibraryFilters Component
 *
 * Touch-optimized filters panel for library games.
 * Provides comprehensive filtering options with progressive disclosure.
 *
 * Touch Optimizations:
 * - Minimum 48px touch targets for all interactive elements
 * - Steam Deck specific sizing (52px+ targets)
 * - Touch-friendly checkboxes and buttons
 * - Simplified interaction model for handheld devices
 * - Progressive disclosure to reduce interface complexity
 *
 * Features:
 * - Status filters (installed/not installed)
 * - Genre filtering with search and categorization
 * - Clear all functionality
 * - Active filter indicators
 * - Responsive layout for different screen sizes
 *
 * Performance Features:
 * - Memoized genre calculations
 * - Efficient filter state management
 * - Optimized re-render cycles
 * - Debounced search functionality
 */

interface LibraryFiltersProps {
  onClearFilters: () => void;
}

const getGenreIcon = (genre: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'Action': ZapIcon,
    'Adventure': LocationIcon,
    'RPG': StarIcon,
    'Strategy': GraphIcon,
    'Simulation': GearIcon,
    'Sports': TrophyIcon,
    'Racing': RocketIcon,
    'Puzzle': CircleIcon,
    'Platformer': ArrowUpIcon,
    'Shooter': ShieldIcon,
    'Fighting': FlameIcon,
    'Horror': MoonIcon,
    'Survival': SquareIcon,
    'Indie': HeartIcon,
    'Casual': SmileyIcon
  };

  return iconMap[genre] || StarIcon;
};

export function LibraryFilters({ onClearFilters }: LibraryFiltersProps) {
  const { t } = useTranslation("library");
  const dispatch = useAppDispatch();

  const { filters } = useAppSelector((state) => state.libraryCollections);
  const { library } = useLibrary();
  const [gameGenres, setGameGenres] = useState<Map<string, string[]>>(new Map());
  const [isLoadingGenres, setIsLoadingGenres] = useState(false);

  // Use predefined common game genres with better matching
  useEffect(() => {
    if (library.length === 0) return;

    setIsLoadingGenres(true);

    // Simulate loading for better UX
    setTimeout(() => {
      const genresMap = generateGameGenresMap(library);
      setGameGenres(genresMap);
      setIsLoadingGenres(false);
    }, 300);
  }, [library]);

  // Helper function to normalize genre names and remove duplicates
  const normalizeGenreName = (genre: string): string | null => {
    const normalized = genre.trim();

    // Map common variations to standard names
    const genreMapping: Record<string, string> = {
      'Role-Playing': 'RPG',
      'role-playing': 'RPG',
      'Role Playing': 'RPG',
      'role playing': 'RPG',
      'ROLE-PLAYING': 'RPG',
      'ROLE PLAYING': 'RPG'
    };

    // Return mapped genre or original if no mapping exists
    return genreMapping[normalized] || normalized;
  };

  // Extract available genres from fetched data, plus show all common genres
  const availableGenres = useMemo(() => {
    const genresSet = new Set<string>(COMMON_GENRES);

    gameGenres.forEach((genres) => {
      genres.forEach((genre) => {
        if (genre && typeof genre === 'string') {
          // Normalize genre names to avoid duplicates
          const normalizedGenre = normalizeGenreName(genre);
          if (normalizedGenre) {
            genresSet.add(normalizedGenre);
          }
        }
      });
    });

    return Array.from(genresSet).sort();
  }, [gameGenres]);

  // Count games per genre using improved counting system
  const genreCounts = useMemo(() => {
    return countGamesByGenre(library);
  }, [library]);



  const handleGenreToggle = useCallback(
    (genre: string, checked: boolean) => {
      let newGenres: string[];

      if (checked) {
        // Add genre if not already present
        newGenres = filters.genres.includes(genre)
          ? filters.genres
          : [...filters.genres, genre];
      } else {
        // Remove genre
        newGenres = filters.genres.filter((g) => g !== genre);
      }

      dispatch(setLibraryFilters({ genres: newGenres }));
    },
    [dispatch, filters.genres]
  );

  const handleInstalledOnlyToggle = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      dispatch(setLibraryFilters({
        showInstalledOnly: e.target.checked,
        showNotInstalledOnly: false // Clear the opposite filter
      }));
    },
    [dispatch]
  );

  const handleNotInstalledOnlyToggle = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      dispatch(setLibraryFilters({
        showNotInstalledOnly: e.target.checked,
        showInstalledOnly: false // Clear the opposite filter
      }));
    },
    [dispatch]
  );



  const hasActiveFilters =
    filters.genres.length > 0 ||
    filters.showInstalledOnly ||
    filters.showNotInstalledOnly ||
    filters.searchQuery.length > 0;

  return (
    <div className="library-filters">
      <div className="library-filters__header">
        <h3 className="library-filters__title">{t("filters")}</h3>
        {hasActiveFilters && (
          <Button
            onClick={onClearFilters}
            theme="outline"
          >
            <XIcon size={14} />
            {t("clear_all")}
          </Button>
        )}
      </div>

      <div className="library-filters__content">
        <div className={`library-filters__section ${(filters.showInstalledOnly || filters.showNotInstalledOnly) ? 'library-filters__section--active' : ''}`}>
          <h4 className="library-filters__section-title">
            <div className="library-filters__section-icon">
              <GearIcon size={16} />
            </div>
            <span className="library-filters__section-text">{t("status")}</span>
            {(filters.showInstalledOnly || filters.showNotInstalledOnly) && (
              <span className="library-filters__active-count">
                {(filters.showInstalledOnly ? 1 : 0) + (filters.showNotInstalledOnly ? 1 : 0)}
              </span>
            )}
          </h4>

          <div className="library-filters__options">
            <CheckboxField
              label={
                <div className="library-filters__checkbox-label">
                  <div className="library-filters__checkbox-icon">
                    <CheckIcon size={14} />
                  </div>
                  <span className="library-filters__checkbox-text">{t("installed_only")}</span>
                </div>
              }
              checked={filters.showInstalledOnly}
              onChange={handleInstalledOnlyToggle}
            />

            <CheckboxField
              label={
                <div className="library-filters__checkbox-label">
                  <div className="library-filters__checkbox-icon">
                    <DownloadIcon size={14} />
                  </div>
                  <span className="library-filters__checkbox-text">{t("not_installed_only")}</span>
                </div>
              }
              checked={filters.showNotInstalledOnly}
              onChange={handleNotInstalledOnlyToggle}
            />
          </div>
        </div>

        <div className={`library-filters__section ${filters.genres.length > 0 ? 'library-filters__section--active' : ''}`}>
          <h4 className="library-filters__section-title">
            <div className="library-filters__section-icon">
              <TagIcon size={16} />
            </div>
            <span className="library-filters__section-text">{t("genres")}</span>
            {filters.genres.length > 0 && (
              <span className="library-filters__active-count">
                {filters.genres.length}
              </span>
            )}
          </h4>

          <div className="library-filters__genre-grid">
            {isLoadingGenres ? (
              <div className="library-filters__loading">
                <div className="library-filters__loading-spinner"></div>
                <span>{t("loading_genres")}...</span>
              </div>
            ) : availableGenres.length > 0 ? (
              availableGenres.map((genre) => {
                const IconComponent = getGenreIcon(genre);
                const isSelected = filters.genres.includes(genre);
                const count = genreCounts[genre] || 0;

                return (
                  <div
                    key={genre}
                    className={`library-filters__genre-chip ${isSelected ? 'library-filters__genre-chip--selected' : ''}`}
                    onClick={() => handleGenreToggle(genre, !isSelected)}
                  >
                    <div className="library-filters__genre-chip-content">
                      <div className="library-filters__genre-chip-icon">
                        <IconComponent size={14} />
                      </div>
                      <span className="library-filters__genre-chip-text">
                        {t(`genre_types.${genre.toLowerCase()}`, genre)}
                      </span>
                      <span className="library-filters__genre-chip-count">
                        {count}
                      </span>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="library-filters__no-genres">
                <TagIcon size={20} />
                <span>{t("no_genres_available")}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
