@use "../../../scss/globals.scss";

.cloud-sync-files-modal {
  &__mapping-methods {
    display: grid;
    gap: globals.$spacing-unit;
    grid-template-columns: repeat(2, 1fr);
  }

  &__file-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
    margin-top: calc(globals.$spacing-unit * 2);
  }

  &__file-item {
    flex: 1;
    color: globals.$muted-color;
    text-decoration: underline;
    display: flex;
    cursor: pointer;
  }

  &__container {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
  }

  &__mapping-label {
    margin-bottom: globals.$spacing-unit;
  }

  &__custom-path {
    margin-top: calc(globals.$spacing-unit * 2);
  }
}
