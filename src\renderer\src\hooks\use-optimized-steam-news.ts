import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { steamNewsService, SteamNewsItem } from '@renderer/services/steam-news-service';
import { useTranslation } from 'react-i18next';
import { getSteamLanguage } from '@renderer/helpers';

interface UseOptimizedSteamNewsOptions {
  enabled?: boolean;
  maxItemsPerGame?: number;
  refreshInterval?: number;
  staleTime?: number; // Time before data is considered stale
  cacheTime?: number; // Time to keep data in cache
}

interface UseOptimizedSteamNewsReturn {
  news: SteamNewsItem[];
  isLoading: boolean;
  isFetching: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  clearCache: () => void;
  isStale: boolean;
}

/**
 * Optimized hook for Steam news with better caching and performance
 */
export function useOptimizedSteamNews(
  appIds: number[],
  options: UseOptimizedSteamNewsOptions = {}
): UseOptimizedSteamNewsReturn {
  const {
    enabled = true,
    maxItemsPerGame = 2,
    refreshInterval = 20 * 60 * 1000, // 20 minutes
    staleTime = 10 * 60 * 1000, // 10 minutes
    cacheTime = 60 * 60 * 1000, // 1 hour
  } = options;

  const { i18n } = useTranslation();
  const [news, setNews] = useState<SteamNewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Using centralized getSteamLanguage function from helpers

  // Memoize app IDs to prevent unnecessary re-fetches
  const memoizedAppIds = useMemo(() => {
    return appIds.slice().sort((a, b) => a - b);
  }, [appIds]);

  // Check if data is stale
  const isStale = useMemo(() => {
    return Date.now() - lastFetchTime > staleTime;
  }, [lastFetchTime, staleTime]);

  // Optimized fetch function with abort support
  const fetchNews = useCallback(async (forceRefresh = false, background = false) => {
    if (!enabled || memoizedAppIds.length === 0) {
      return;
    }

    // Cancel previous request if still running
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Don't fetch if data is fresh and not forcing refresh
    if (!forceRefresh && !isStale && news.length > 0) {
      console.log('📦 Using fresh cached data');
      return;
    }

    abortControllerRef.current = new AbortController();
    
    if (!background) {
      setIsLoading(true);
    }
    setIsFetching(true);
    setError(null);

    try {
      const steamLanguage = getSteamLanguage(i18n.language);
      console.log(`🌍 Fetching news for ${memoizedAppIds.length} games in ${steamLanguage}`);

      // Only clear cache if explicitly requested
      if (forceRefresh) {
        console.log('🗑️ Force refresh: clearing cache');
        steamNewsService.clearCache();
      }

      const newsItems = await steamNewsService.getLibraryNews(
        memoizedAppIds, 
        maxItemsPerGame, 
        steamLanguage
      );

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      setNews(newsItems);
      setLastFetchTime(Date.now());

      console.log(`📰 Successfully fetched ${newsItems.length} news items`);
    } catch (err) {
      // Don't set error if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch news';
      setError(errorMessage);
      console.error('Steam news fetch error:', err);
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setIsLoading(false);
        setIsFetching(false);
      }
    }
  }, [enabled, memoizedAppIds, maxItemsPerGame, i18n.language, getSteamLanguage, isStale, news.length]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    await fetchNews(true, false);
  }, [fetchNews]);

  // Clear cache function
  const clearCache = useCallback(() => {
    steamNewsService.clearCache();
    setNews([]);
    setLastFetchTime(0);
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchNews(false, false);
  }, [fetchNews]);

  // Auto-refresh interval with background fetching
  useEffect(() => {
    if (!refreshInterval || !enabled) {
      return;
    }

    const interval = setInterval(() => {
      fetchNews(false, true); // Background refresh
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [fetchNews, refreshInterval, enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    news,
    isLoading,
    isFetching,
    error,
    refresh,
    clearCache,
    isStale,
  };
}
