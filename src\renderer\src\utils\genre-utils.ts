import type { LibraryGame } from "@types";

export interface GenrePatterns {
  [key: string]: {
    keywords: string[];
    weight: number;
    exclusions?: string[];
  };
}

export interface GameSeries {
  [key: string]: {
    genres: string[];
    keywords: string[];
  };
}

// Comprehensive genre patterns with weights and exclusions
export const GENRE_PATTERNS: GenrePatterns = {
  'action': {
    keywords: ['action', 'combat', 'fight', 'battle', 'war', 'warfare', 'conflict', 'assault', 'strike', 'ops', 'force'],
    weight: 1.0
  },
  'adventure': {
    keywords: ['adventure', 'quest', 'journey', 'explore', 'discovery', 'expedition', 'legend', 'tale', 'chronicles'],
    weight: 1.0
  },
  'rpg': {
    keywords: ['rpg', 'role-playing', 'role playing', 'fantasy', 'magic', 'wizard', 'dragon', 'elder scrolls', 'witcher', 'final fantasy'],
    weight: 1.2
  },
  'strategy': {
    keywords: ['strategy', 'tactical', 'empire', 'civilization', 'command', 'conquer', 'total war', 'age of'],
    weight: 1.0
  },
  'simulation': {
    keywords: ['sim', 'simulator', 'tycoon', 'manager', 'farming', 'city', 'truck', 'flight', 'train'],
    weight: 1.0
  },
  'sports': {
    keywords: ['sport', 'football', 'soccer', 'basketball', 'tennis', 'golf', 'baseball', 'hockey', 'fifa', 'nba', 'nfl', 'nhl'],
    weight: 1.3
  },
  'racing': {
    keywords: ['racing', 'race', 'speed', 'car', 'formula', 'rally', 'drive', 'motor', 'gran turismo', 'forza', 'need for speed'],
    weight: 1.2
  },
  'puzzle': {
    keywords: ['puzzle', 'brain', 'logic', 'match', 'tetris', 'sudoku', 'word', 'portal'],
    weight: 1.0
  },
  'platformer': {
    keywords: ['platform', 'jump', 'mario', 'sonic', 'side-scroll', 'metroidvania'],
    weight: 1.1
  },
  'shooter': {
    keywords: ['shooter', 'shoot', 'fps', 'gun', 'sniper', 'call of duty', 'battlefield', 'counter-strike', 'doom', 'halo'],
    weight: 1.2
  },
  'fighting': {
    keywords: ['fight', 'martial', 'boxing', 'wrestling', 'tekken', 'street fighter', 'mortal kombat'],
    weight: 1.2,
    exclusions: ['action'] // Don't double-count as action if it's clearly fighting
  },
  'horror': {
    keywords: ['horror', 'scary', 'zombie', 'dead', 'evil', 'fear', 'nightmare', 'resident evil', 'silent hill', 'outlast'],
    weight: 1.3,
    exclusions: ['red dead'] // Exclude games that might have "dead" but aren't horror
  },
  'survival': {
    keywords: ['survival', 'survive', 'craft', 'build', 'resource', 'wilderness', 'minecraft', 'rust', 'ark'],
    weight: 1.1
  },
  'indie': {
    keywords: ['indie', 'independent', 'pixel', 'retro', '8-bit', '16-bit'],
    weight: 0.8 // Lower weight as it's more of a production style
  },
  'casual': {
    keywords: ['casual', 'simple', 'easy', 'family', 'relaxing', 'zen', 'chill', 'mobile'],
    weight: 0.7
  },
  'western': {
    keywords: ['western', 'cowboy', 'outlaw', 'frontier', 'wild west', 'red dead'],
    weight: 1.2
  },
  'stealth': {
    keywords: ['stealth', 'sneak', 'assassin', 'hitman', 'metal gear', 'splinter cell'],
    weight: 1.1
  }
};

// Known game series with their typical genres
export const GAME_SERIES: GameSeries = {
  'red dead': {
    genres: ['Action', 'Adventure', 'Western', 'Shooter'],
    keywords: ['red dead redemption', 'red dead']
  },
  'grand theft auto': {
    genres: ['Action', 'Adventure', 'Crime'],
    keywords: ['grand theft auto', 'gta']
  },
  'call of duty': {
    genres: ['Shooter', 'Action', 'War'],
    keywords: ['call of duty', 'cod']
  },
  'battlefield': {
    genres: ['Shooter', 'Action', 'War'],
    keywords: ['battlefield']
  },
  'assassins creed': {
    genres: ['Action', 'Adventure', 'Stealth', 'Historical'],
    keywords: ['assassin\'s creed', 'assassins creed']
  },
  'elder scrolls': {
    genres: ['RPG', 'Fantasy', 'Adventure'],
    keywords: ['elder scrolls', 'skyrim', 'oblivion', 'morrowind']
  },
  'witcher': {
    genres: ['RPG', 'Fantasy', 'Adventure'],
    keywords: ['witcher']
  },
  'fallout': {
    genres: ['RPG', 'Post-Apocalyptic', 'Adventure'],
    keywords: ['fallout']
  },
  'fifa': {
    genres: ['Sports', 'Soccer'],
    keywords: ['fifa']
  },
  'nba': {
    genres: ['Sports', 'Basketball'],
    keywords: ['nba 2k', 'nba live']
  },
  'forza': {
    genres: ['Racing', 'Simulation'],
    keywords: ['forza']
  },
  'need for speed': {
    genres: ['Racing', 'Action'],
    keywords: ['need for speed', 'nfs']
  },
  'resident evil': {
    genres: ['Horror', 'Survival', 'Action'],
    keywords: ['resident evil']
  },
  'silent hill': {
    genres: ['Horror', 'Psychological'],
    keywords: ['silent hill']
  },
  'metal gear': {
    genres: ['Stealth', 'Action', 'Tactical'],
    keywords: ['metal gear']
  },
  'civilization': {
    genres: ['Strategy', 'Turn-Based'],
    keywords: ['civilization', 'civ']
  },
  'total war': {
    genres: ['Strategy', 'Real-Time', 'Historical'],
    keywords: ['total war']
  },
  'minecraft': {
    genres: ['Survival', 'Sandbox', 'Indie'],
    keywords: ['minecraft']
  },
  'portal': {
    genres: ['Puzzle', 'Adventure', 'Sci-Fi'],
    keywords: ['portal']
  },
  'half-life': {
    genres: ['Shooter', 'Sci-Fi', 'Adventure'],
    keywords: ['half-life', 'half life']
  }
};

export const COMMON_GENRES = [
  "Action", "Adventure", "RPG", "Strategy", "Simulation",
  "Sports", "Racing", "Puzzle", "Platformer", "Shooter",
  "Fighting", "Horror", "Survival", "Indie", "Casual",
  "Western", "Stealth"
];

/**
 * Assigns multiple genres to a game based on sophisticated detection
 */
export function assignGenresToGame(game: LibraryGame, gameIndex: number): string[] {
  const gameTitle = game.title.toLowerCase();
  const genreScores: { [key: string]: number } = {};

  // First, check for known game series (highest priority)
  for (const [seriesName, seriesData] of Object.entries(GAME_SERIES)) {
    const matchesSeries = seriesData.keywords.some(keyword =>
      gameTitle.includes(keyword.toLowerCase())
    );

    if (matchesSeries) {
      // Add all genres from the series with high confidence
      seriesData.genres.forEach(genre => {
        genreScores[genre] = (genreScores[genre] || 0) + 2.0;
      });
    }
  }

  // Then check genre patterns with weighted scoring
  Object.entries(GENRE_PATTERNS).forEach(([genre, patternData]) => {
    // Map genre names to match COMMON_GENRES exactly
    const genreMapping: Record<string, string> = {
      'rpg': 'RPG',
      'action': 'Action',
      'adventure': 'Adventure',
      'strategy': 'Strategy',
      'simulation': 'Simulation',
      'sports': 'Sports',
      'racing': 'Racing',
      'puzzle': 'Puzzle',
      'platformer': 'Platformer',
      'shooter': 'Shooter',
      'fighting': 'Fighting',
      'horror': 'Horror',
      'survival': 'Survival',
      'indie': 'Indie',
      'casual': 'Casual',
      'western': 'Western',
      'stealth': 'Stealth'
    };

    const genreName = genreMapping[genre] || genre.charAt(0).toUpperCase() + genre.slice(1);

    // Check if this genre should be excluded for this game
    if (patternData.exclusions) {
      const shouldExclude = patternData.exclusions.some(exclusion =>
        gameTitle.includes(exclusion.toLowerCase())
      );
      if (shouldExclude) return;
    }

    // Calculate score based on keyword matches
    let score = 0;
    patternData.keywords.forEach(keyword => {
      if (gameTitle.includes(keyword.toLowerCase())) {
        // Exact word boundary match gets higher score
        const wordBoundaryRegex = new RegExp(`\\b${keyword.toLowerCase()}\\b`, 'i');
        if (wordBoundaryRegex.test(gameTitle)) {
          score += patternData.weight * 1.5;
        } else {
          score += patternData.weight;
        }
      }
    });

    if (score > 0) {
      genreScores[genreName] = (genreScores[genreName] || 0) + score;
    }
  });

  // Additional contextual analysis
  addContextualGenres(gameTitle, genreScores);

  // Convert scores to final genre list
  const finalGenres: string[] = [];

  // Sort genres by score and select the most relevant ones
  const sortedGenres = Object.entries(genreScores)
    .sort(([, a], [, b]) => b - a)
    .filter(([, score]) => score >= 0.8); // Minimum threshold

  // Take top genres, but ensure we don't have too many
  const maxGenres = 4;
  sortedGenres.slice(0, maxGenres).forEach(([genre]) => {
    finalGenres.push(genre);
  });

  // Fallback system if no genres detected
  if (finalGenres.length === 0) {
    finalGenres.push(...getFallbackGenres(gameTitle, gameIndex));
  }

  return finalGenres;
}

/**
 * Adds contextual genres based on game title analysis
 */
function addContextualGenres(gameTitle: string, genreScores: { [key: string]: number }): void {
  // Year detection for sports games
  const yearMatch = gameTitle.match(/\b(19|20)\d{2}\b/);
  if (yearMatch) {
    if (gameTitle.includes('fifa') || gameTitle.includes('nba') || gameTitle.includes('nfl') || gameTitle.includes('nhl')) {
      genreScores['Sports'] = (genreScores['Sports'] || 0) + 1.5;
    }
  }

  // Roman numerals often indicate sequels in RPGs or Action games
  const romanNumerals = /\b(ii|iii|iv|v|vi|vii|viii|ix|x)\b/i;
  if (romanNumerals.test(gameTitle)) {
    if (gameTitle.includes('final fantasy') || gameTitle.includes('elder scrolls')) {
      genreScores['RPG'] = (genreScores['RPG'] || 0) + 1.0;
    }
  }

  // Subtitle analysis
  if (gameTitle.includes('remastered') || gameTitle.includes('definitive') || gameTitle.includes('enhanced')) {
    // These are often action or adventure games
    genreScores['Action'] = (genreScores['Action'] || 0) + 0.5;
  }

  // VR games are often action or simulation
  if (gameTitle.includes('vr') || gameTitle.includes('virtual reality')) {
    genreScores['Simulation'] = (genreScores['Simulation'] || 0) + 0.8;
  }
}

/**
 * Provides fallback genres when detection fails
 */
function getFallbackGenres(gameTitle: string, gameIndex: number): string[] {
  const fallbackGenres: string[] = [];

  // Use a more sophisticated fallback based on title characteristics
  if (gameTitle.length > 20) {
    // Longer titles often indicate RPGs or Adventure games
    fallbackGenres.push('Adventure');
  } else if (gameTitle.length < 10) {
    // Shorter titles might be indie or casual games
    fallbackGenres.push('Indie');
  } else {
    // Medium length titles, use index-based assignment
    const primaryGenres = ['Action', 'Adventure', 'RPG', 'Strategy', 'Simulation'];
    fallbackGenres.push(primaryGenres[gameIndex % primaryGenres.length]);
  }

  // Add a secondary genre for variety
  const secondaryGenres = ['Casual', 'Indie', 'Puzzle'];
  if (gameIndex % 3 === 0) {
    fallbackGenres.push(secondaryGenres[gameIndex % secondaryGenres.length]);
  }

  return fallbackGenres;
}

/**
 * Checks if a game matches a specific genre
 */
export function gameMatchesGenre(game: LibraryGame, genre: string, gameIndex: number): boolean {
  const assignedGenres = assignGenresToGame(game, gameIndex);
  return assignedGenres.some(assignedGenre =>
    assignedGenre.toLowerCase() === genre.toLowerCase()
  );
}

/**
 * Generates a map of game IDs to their assigned genres
 */
export function generateGameGenresMap(games: LibraryGame[]): Map<string, string[]> {
  const genresMap = new Map<string, string[]>();

  games.forEach((game, index) => {
    const genres = assignGenresToGame(game, index);
    if (genres.length > 0) {
      genresMap.set(game.id, genres);
    }
  });

  return genresMap;
}

/**
 * Counts how many games belong to each genre
 */
export function countGamesByGenre(games: LibraryGame[]): { [genre: string]: number } {
  const genreCounts: { [genre: string]: number } = {};

  // Initialize all common genres with 0
  COMMON_GENRES.forEach(genre => {
    genreCounts[genre] = 0;
  });

  games.forEach((game, index) => {
    const genres = assignGenresToGame(game, index);
    genres.forEach(genre => {
      genreCounts[genre] = (genreCounts[genre] || 0) + 1;
    });
  });

  return genreCounts;
}

/**
 * Gets all unique genres from a list of games
 */
export function getUniqueGenres(games: LibraryGame[]): string[] {
  const genreSet = new Set<string>();

  games.forEach((game, index) => {
    const genres = assignGenresToGame(game, index);
    genres.forEach(genre => genreSet.add(genre));
  });

  // Combine with common genres and sort
  const allGenres = Array.from(new Set([...COMMON_GENRES, ...Array.from(genreSet)]));
  return allGenres.sort();
}

/**
 * Filters games by multiple genres (AND logic)
 */
export function filterGamesByGenres(games: LibraryGame[], selectedGenres: string[]): LibraryGame[] {
  if (selectedGenres.length === 0) return games;

  return games.filter((game, index) => {
    const gameGenres = assignGenresToGame(game, index);
    // Game must have at least one of the selected genres
    return selectedGenres.some(selectedGenre =>
      gameGenres.some(gameGenre =>
        gameGenre.toLowerCase() === selectedGenre.toLowerCase()
      )
    );
  });
}
