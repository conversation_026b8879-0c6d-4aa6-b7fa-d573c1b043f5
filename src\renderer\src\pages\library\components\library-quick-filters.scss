@use "../../../scss/globals.scss";

.library-quick-filters {
  margin-bottom: calc(globals.$spacing-unit * 2);
  animation: filtersSlideIn 0.3s ease-out;

  &__content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    flex-wrap: wrap;
  }

  &__chip {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.75);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: globals.$small-font-size;
    font-weight: 500;
    color: globals.$muted-color;
    transition: all 0.2s ease;
    animation: chipSlideIn 0.2s ease-out;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
    }

    &--search {
      background: rgba(22, 177, 149, 0.1);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;

      &:hover {
        background: rgba(22, 177, 149, 0.15);
        border-color: rgba(22, 177, 149, 0.4);
      }

      .library-quick-filters__chip-remove {
        color: globals.$brand-teal;

        &:hover {
          background: rgba(22, 177, 149, 0.2);
          color: white;
        }
      }
    }

    &--filter {
      &:hover {
        transform: translateY(-1px) scale(1.02);
      }
    }

    &--genre {
      background: rgba(62, 98, 192, 0.1);
      border-color: rgba(62, 98, 192, 0.3);
      color: globals.$brand-blue;

      &:hover {
        background: rgba(62, 98, 192, 0.15);
        border-color: rgba(62, 98, 192, 0.4);
      }

      .library-quick-filters__chip-icon {
        color: globals.$brand-blue;
      }

      .library-quick-filters__chip-remove {
        color: globals.$brand-blue;

        &:hover {
          background: rgba(62, 98, 192, 0.2);
          color: white;
        }
      }
    }

    &--sort {
      background: rgba(255, 193, 7, 0.1);
      border-color: rgba(255, 193, 7, 0.3);
      color: globals.$warning-color;

      &:hover {
        background: rgba(255, 193, 7, 0.15);
        border-color: rgba(255, 193, 7, 0.4);
      }

      .library-quick-filters__chip-icon {
        color: globals.$warning-color;
      }

      .library-quick-filters__chip-remove {
        color: globals.$warning-color;

        &:hover {
          background: rgba(255, 193, 7, 0.2);
          color: white;
        }
      }
    }
  }

  &__chip-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  &__chip-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;

    @media (max-width: 768px) {
      max-width: 150px;
    }
  }

  &__chip-remove {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: transparent;
    border: none;
    border-radius: 50%;
    color: inherit;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__clear-all {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1.25);
    background: rgba(128, 29, 30, 0.1);
    border: 1px solid rgba(128, 29, 30, 0.3);
    border-radius: 20px;
    font-size: globals.$small-font-size;
    font-weight: 600;
    color: globals.$danger-color;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: calc(globals.$spacing-unit * 1);

    &:hover {
      background: rgba(128, 29, 30, 0.15);
      border-color: rgba(128, 29, 30, 0.4);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0) scale(0.98);
    }

    @media (max-width: 768px) {
      margin-left: 0;
      margin-top: calc(globals.$spacing-unit * 0.5);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .library-quick-filters {
    &__content {
      gap: calc(globals.$spacing-unit * 0.75);
    }

    &__chip {
      padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
      gap: calc(globals.$spacing-unit * 0.5);
      font-size: 11px;
    }

    &__chip-label {
      max-width: 120px;
    }

    &__clear-all {
      padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 1);
      font-size: 11px;
    }
  }
}

// Animations
@keyframes filtersSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes chipSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
