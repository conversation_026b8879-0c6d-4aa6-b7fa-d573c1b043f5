@use "../../../scss/globals.scss";

.library-search {
  position: relative;
  width: 100%;
  max-width: 400px;

  &__input-container {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &--focused {
      .library-search__input-wrapper {
        border-color: rgba(22, 177, 149, 0.6);
        box-shadow:
          0 0 0 3px rgba(22, 177, 149, 0.15),
          0 4px 24px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
      }

      .library-search__search-icon {
        color: rgba(22, 177, 149, 0.9);
        transform: scale(1.1);
      }
    }
  }

  &__input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 14px;
    padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.5);
    backdrop-filter: blur(24px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    gap: calc(globals.$spacing-unit * 1);

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      border-color: rgba(255, 255, 255, 0.18);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.18);
      transform: translateY(-1px);
    }
  }

  &__search-icon {
    color: globals.$body-color;
    transition: all 0.3s ease;
    flex-shrink: 0;
    opacity: 0.8;
  }

  &__input {
    flex: 1;
    background: transparent;
    border: none;
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1.5);
    margin: 0;
    color: globals.$muted-color;
    font-size: globals.$body-font-size;
    font-family: inherit;
    min-height: auto;
    height: auto;
    outline: none;

    &::placeholder {
      color: globals.$body-color;
      opacity: 0.8;
      font-weight: 500;
    }

    &:focus {
      outline: none;
      box-shadow: none;
      border: none;
    }
  }

  &__clear-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 6px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: globals.$muted-color;
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: calc(globals.$spacing-unit * 0.5);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    backdrop-filter: blur(24px);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.6),
      0 6px 24px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 9998;
    animation: suggestionsSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    max-width: 100%; // Ensure suggestions don't overflow container

    // Better positioning on mobile
    @media (max-width: 768px) {
      margin-top: calc(globals.$spacing-unit * 0.25);
      border-radius: 12px;
    }
  }

  &__suggestions-header {
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  }

  &__suggestions-title {
    font-size: globals.$small-font-size;
    font-weight: 700;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__suggestions-list {
    padding: calc(globals.$spacing-unit * 1);
    max-height: 200px;
    overflow-y: auto;
  }

  &__suggestion {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    width: 100%;
    background: transparent;
    border: none;
    padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.5);
    color: globals.$muted-color;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    border-radius: 10px;
    font-size: globals.$body-font-size;
    font-weight: 500;
    margin-bottom: calc(globals.$spacing-unit * 0.5);

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.06) 100%);
      color: white;
      transform: translateX(4px);
    }

    &:focus {
      outline: 2px solid rgba(62, 98, 192, 0.4);
      outline-offset: 2px;
    }

    &:active {
      transform: translateX(2px);
    }
  }

  &__suggestion-icon {
    opacity: 0.5;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
  }

  &__suggestion-text {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__suggestion:hover &__suggestion-icon {
    opacity: 0.8;
  }

  &__stats {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: calc(globals.$spacing-unit * 0.5);
    padding: 0 calc(globals.$spacing-unit * 1.5);
    z-index: 1;
    pointer-events: none;
  }

  &__stats-text {
    font-size: globals.$small-font-size;
    color: globals.$body-color;
    opacity: 0.8;
    font-weight: 500;
  }

  // Scrollbar styling for suggestions
  &__suggestions-list::-webkit-scrollbar {
    width: 6px;
  }

  &__suggestions-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &__suggestions-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
    }
  }
}

@keyframes suggestionsSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .library-search {
    max-width: 100%;

    &__input-wrapper {
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    }

    &__input {
      font-size: globals.$small-font-size;
    }

    &__suggestions {
      margin-top: calc(globals.$spacing-unit * 0.25);
    }

    &__suggestion {
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
      font-size: globals.$small-font-size;
    }
  }
}
