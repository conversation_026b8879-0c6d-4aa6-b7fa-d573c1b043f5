@use "../../scss/globals.scss";
@use "sass:color";

.theme-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  &__header {
    display: flex;
    align-items: center;
    padding: calc(globals.$spacing-unit + 1px);
    background-color: globals.$dark-background-color;
    font-size: 8px;
    z-index: 50;
    -webkit-app-region: drag;
    gap: 8px;

    &--darwin {
      padding-top: calc(globals.$spacing-unit * 6);
    }

    h1 {
      margin: 0;
      line-height: 1;
    }

    &__status {
      display: flex;
      width: 9px;
      height: 9px;
      background-color: globals.$muted-color;
      border-radius: 50%;
      margin-top: 3px;
    }
  }

  &__editor {
    position: relative;
    width: 100%;
    height: 100%;
    flex: 1;
  }

  &__notification-preview-wrapper {
    position: relative;
    border: 1px solid globals.$muted-color;
    border-radius: 2px;
  }

  &__footer {
    display: flex;
    flex-direction: column;
    background-color: globals.$dark-background-color;
    padding: globals.$spacing-unit globals.$spacing-unit * 2;
    gap: 24px;

    &-actions {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      &__tabs {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 8px;

        .active {
          background-color: color.adjust(
            globals.$dark-background-color,
            $lightness: -2%
          );
        }
      }
    }
  }

  &__info {
    padding: 16px;

    p {
      font-size: 16px;
      font-weight: 600;
      color: globals.$muted-color;
      margin-bottom: 8px;
    }
  }

  &__notification-preview {
    padding-top: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;

    &__select-variation {
      flex: inherit;
    }
  }
}
