@use "../../../scss/globals.scss";

.sidebar-section {
  &__button {
    height: 72px;
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 2);
    display: flex;
    align-items: center;
    background-color: globals.$background-color;
    color: globals.$muted-color;
    width: 100%;
    cursor: pointer;
    transition: all ease 0.2s;
    gap: globals.$spacing-unit;
    font-size: globals.$body-font-size;
    font-weight: bold;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    &:active {
      opacity: globals.$active-opacity;
    }
  }

  &__chevron {
    transition: transform ease 0.2s;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__content {
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0, 1, 0, 1);
    position: relative;
  }
}
