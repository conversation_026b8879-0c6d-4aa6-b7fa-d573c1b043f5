@use "../../../scss/globals.scss";

.library-game-list {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.5s ease-out;
  margin: calc(globals.$spacing-unit * 4);
  margin-bottom: calc(globals.$spacing-unit * 6);
  box-sizing: border-box;

  &__empty {
    text-align: center;
    padding: calc(globals.$spacing-unit * 4);
    color: globals.$body-color;
  }

  &__header {
    display: grid;
    grid-template-columns: 1fr auto auto auto auto;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 700;
    font-size: globals.$small-font-size;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr auto;
      
      &-cell--last-played,
      &-cell--playtime {
        display: none;
      }
    }
  }

  &__header-cell {
    display: flex;
    align-items: center;

    &--game {
      justify-content: flex-start;
    }

    &--playtime,
    &--last-played,
    &--status {
      justify-content: center;
      min-width: 120px;
    }
  }

  &__content {
    overflow-y: visible;
  }

  &__item {
    display: grid;
    grid-template-columns: 1fr auto auto auto auto;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 3);
    background: transparent;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    width: 100%;
    align-items: center;
    position: relative;

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
      transform: translateX(4px);

      .library-game-list__options {
        opacity: 1;
      }

      .library-game-list__image {
        transform: scale(1.05);
      }
    }

    &:active {
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(2px);
    }

    &:last-child {
      border-bottom: none;
    }
  }

  &__options {
    opacity: 0;
    transition: opacity ease 0.2s;
    position: relative;
  }

  &__options-button {
    background: transparent;
    border: 1px solid globals.$border-color;
    border-radius: 4px;
    padding: calc(globals.$spacing-unit / 2);
    color: globals.$body-color;
    cursor: pointer;
    transition: all ease 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.3);
      color: globals.$muted-color;
    }
  }

  &__options-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: calc(globals.$spacing-unit / 2);
    background-color: globals.$dark-background-color;
    border: 1px solid globals.$border-color;
    border-radius: 6px;
    padding: calc(globals.$spacing-unit / 2);
    min-width: 160px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    z-index: 10;
  }

  &__options-item {
    display: block;
    width: 100%;
    background: transparent;
    border: none;
    padding: calc(globals.$spacing-unit / 2) globals.$spacing-unit;
    color: globals.$muted-color;
    cursor: pointer;
    transition: all ease 0.2s;
    text-align: left;
    border-radius: 4px;
    font-size: globals.$small-font-size;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }

    &:not(:last-child) {
      margin-bottom: calc(globals.$spacing-unit / 4);
    }
  }

  &__game-info {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    min-width: 0;
  }

  &__game-image {
    width: 56px;
    height: 56px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &__image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, globals.$background-color, globals.$dark-background-color);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__placeholder-icon {
    width: 24px;
    height: 24px;
    opacity: 0.3;
  }

  &__game-details {
    min-width: 0;
    flex: 1;
  }

  &__title-container {
    display: flex;
    align-items: center;
    gap: globals.$spacing-unit;
  }

  &__shop-icon {
    width: 16px;
    height: 16px;
    min-width: 16px;
    opacity: 0.7;
  }

  &__title {
    margin: 0;
    font-size: globals.$body-font-size;
    font-weight: 500;
    color: globals.$muted-color;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__playtime,
  &__last-played {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit / 2);
    font-size: globals.$small-font-size;
    color: globals.$body-color;
    min-width: 120px;

    svg {
      opacity: 0.7;
    }
  }

  &__status {
    display: flex;
    justify-content: center;
    min-width: 120px;
  }

  &__status-badge {
    padding: calc(globals.$spacing-unit / 2) globals.$spacing-unit;
    border-radius: 12px;
    font-size: globals.$small-font-size;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit / 2);

    &--playable {
      background-color: globals.$success-color;
      color: white;
    }

    &--not-installed {
      background-color: rgba(255, 255, 255, 0.1);
      color: globals.$body-color;
    }
  }

  // Media queries
  @media (max-width: 768px) {
    margin: calc(globals.$spacing-unit * 2);

    &__header,
    &__item {
      grid-template-columns: 1fr auto;
    }

    &__playtime,
    &__last-played {
      display: none;
    }
  }

  @media (max-width: 480px) {
    margin: calc(globals.$spacing-unit * 1.5);
  }
}
