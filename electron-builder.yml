appId: gg.hydralauncher.hydra
productName: Hydra
directories:
  buildResources: build
extraResources:
  - ludusavi
  - hydra-python-rpc
  - seeds
  - from: node_modules/create-desktop-shortcuts/src/windows.vbs
  - from: resources/achievement.wav
files:
  - "!**/.vscode/*"
  - "!src/*"
  - "!electron.vite.config.{js,ts,mjs,cjs}"
  - "!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}"
  - "!{.env,.env.*,.npmrc,pnpm-lock.yaml}"
  - "!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}"
asarUnpack:
  - resources/**
win:
  executableName: Hydra
  extraResources:
    - from: binaries/aria2c.exe
    - from: binaries/7z.exe
    - from: binaries/7z.dll
  target:
    - nsis
    - portable
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  oneClick: false
  allowToChangeInstallationDirectory: true
  include: installer.nsh
portable:
  artifactName: ${name}-${version}-portable.${ext}
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extraResources:
    - from: binaries/7zz
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  extraResources:
    - from: binaries/7zzs
    - from: binaries/aria2c
  target:
    - AppImage
    - snap
    - deb
    - pacman
    - rpm
  maintainer: electronjs.org
  category: Game
  mimeTypes:
    - x-scheme-handler/hydralauncher
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: github
  owner: hydralauncher
  repo: hydra
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
