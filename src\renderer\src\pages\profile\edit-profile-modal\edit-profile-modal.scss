@use "../../../scss/globals.scss";

.edit-profile-modal {
  &__form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 350px;
  }

  &__content {
    gap: calc(globals.$spacing-unit * 3);
    display: flex;
    flex-direction: column;
  }

  &__hint {
    margin-top: calc(globals.$spacing-unit * 2);
  }

  &__submit {
    align-self: end;
    margin-top: calc(globals.$spacing-unit * 3);
    width: 100%;
  }

  &__avatar-container {
    align-self: center;
    display: flex;
    color: globals.$body-color;
    justify-content: center;
    align-items: center;
    background-color: globals.$background-color;
    position: relative;
    cursor: pointer;
  }

  &__avatar-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    color: globals.$muted-color;
    z-index: 1;
    cursor: pointer;
    display: flex;
    justify-content: center;
    transition: all ease 0.2s;
    align-items: center;
    border-radius: 4px;
    opacity: 0;
  }

  &__avatar-container:hover &__avatar-overlay {
    opacity: 1;
  }
}
